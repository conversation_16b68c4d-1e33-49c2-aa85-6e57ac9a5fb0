<template>
  <el-dialog title="导出" :visible.sync="dialogVisible" width="620px" v-on="$listeners">
    <div class="card-item">
      <div class="card-item-header">
        <p class="titleLine">班级成绩单(EXCEL)</p>
        <el-button class="card-button" type="primary" @click="exportScoreCard">导出</el-button>
      </div>

      <!-- 指标筛选 -->
      <div v-if="indicatorList.length > 0" class="exprot-box">
        <el-checkbox
          style="margin-bottom: 15px"
          :indeterminate="indicator.isIndeterminate"
          v-model="indicator.checkAll"
          @change="handleIndicatorCheckAllChange"
          >全选</el-checkbox
        >
        <el-checkbox-group
          class="checkbox-type-group"
          v-model="indicator.checkList"
          @change="handleIndicatorCheckedChange"
        >
          <el-checkbox
            class="checkbox"
            v-for="item in indicatorList"
            :label="item.value || item.label"
            :key="item.value || item.label"
            >{{ item.label }}</el-checkbox
          >
        </el-checkbox-group>
      </div>
    </div>

    <div class="card-item">
      <p class="titleLine">学生个人成绩单</p>

      <div style="margin-bottom: 15px">
        <el-radio-group v-model="docType">
          <el-radio v-for="item in docTypeOptions" :key="item.value" :label="item.value">{{ item.label }}</el-radio>
        </el-radio-group>
      </div>

      <div style="margin-bottom: 15px">
        <el-checkbox :indeterminate="isIndeterminate" v-model="checkAll" @change="handleCheckAllChange"
          >全选</el-checkbox
        >
      </div>

      <div class="checkbox-box">
        <el-checkbox-group class="checkbox-group" v-model="checkedOptions" @change="handleCheckedCardsChange">
          <el-checkbox v-for="item in scoreOptions" :disabled="item.disabled" :label="item.label" :key="item">{{
            item.label
          }}</el-checkbox>
        </el-checkbox-group>
        <el-checkbox-group
          v-if="ruleOptions.length > 0"
          class="checkbox-group"
          v-model="checkedOptions"
          @change="handleCheckedCardsChange"
        >
          <el-checkbox v-for="item in ruleOptions" :disabled="item.disabled" :label="item.label" :key="item">{{
            item.label
          }}</el-checkbox>
        </el-checkbox-group>
        <div>
          <el-checkbox-group v-model="checkedOptions" @change="handleCheckedCardsChange">
            <el-checkbox v-for="item in clsOptions" :disabled="item.disabled" :label="item.label" :key="item">{{
              item.label
            }}</el-checkbox>
          </el-checkbox-group>
          <el-checkbox-group v-model="checkedOptions" @change="handleCheckedCardsChange">
            <el-checkbox v-for="item in grdOptions" :disabled="item.disabled" :label="item.label" :key="item">{{
              item.label
            }}</el-checkbox>
          </el-checkbox-group>
        </div>
      </div>

      <div style="text-align: right">
        <el-button class="card-button" type="primary" @click="exportStuScoreCard">导出</el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script lang="ts">
import UserRole from '@/utils/UserRole';
import { Component, Prop, Vue } from 'vue-property-decorator';

const scoreOptions = [
  {
    label: '满分',
    value: 'full',
    disabled: false,
  },
  {
    label: '得分',
    value: 'score',
    disabled: false,
  },
  {
    label: '等级',
    value: 'lv',
    disabled: false,
  },
  {
    label: '小题明细',
    value: 'qs',
    disabled: false,
  },
];

const ruleOptions = [
  {
    label: '赋分',
    value: 'rule',
    disabled: false,
  },
  {
    label: '赋分等级',
    value: 'ruleLv',
    disabled: false,
  },
];

const clsOptions = [
  {
    label: '班级排名',
    value: 'rank',
    disabled: false,
  },
  {
    label: '班级最高分',
    value: 'max',
    disabled: false,
  },
  {
    label: '班级平均分',
    value: 'avg',
    disabled: false,
  },
];

const grdOptions = [
  {
    label: '年级排名',
    value: 'rank',
    disabled: false,
  },
  {
    label: '年级最高分',
    value: 'max',
    disabled: false,
  },
  {
    label: '年级平均分',
    value: 'avg',
    disabled: false,
  },
];

const docTypeOptions = [
  {
    label: 'PDF',
    value: 'pdf',
    url: '/phelp/_/exp-stu-card-pdf',
  },
  {
    label: 'EXCEL',
    value: 'excel',
    url: '/pexam/_/exportStuCard',
  },
];

@Component
export default class ReportExportDialog extends Vue {
  // 筛选数据
  @Prop() filterData: any;
  // 对比考试id
  @Prop({ default: '' }) withExamId: string;
  // 是否赋分
  @Prop({ default: false }) isRule: boolean;
  // 是否显示年级排名
  @Prop({ default: true }) isGrdRankEnable: boolean;
  // 是否显示班级排名
  @Prop({ default: true }) isClsRankEnable: boolean;
  // 只显示等级
  @Prop({ default: 0 }) onlyLv;
  @Prop({ type: Array, default: () => [] }) indicatorList: any[];
  @Prop({ type: Array, default: () => [] }) defaultIndicators: any[];

  dialogVisible: boolean = true;

  // 指标筛选
  indicator = {
    list: [],
    checkList: [],
    isIndeterminate: true,
    checkAll: false,
  };

  // 文档类型选项
  docTypeOptions = docTypeOptions;
  // 当前文档类型
  docType = docTypeOptions[0].value;

  // 是否全选
  checkAll = false;
  // 是否不确定选择
  isIndeterminate = true;
  // 已选数据
  checkedOptions = [];
  // 分数指标
  scoreOptions = scoreOptions;
  // 赋分指标
  ruleOptions = ruleOptions;
  // 班级指标
  clsOptions = clsOptions;
  // 年级指标
  grdOptions = grdOptions;
  // 指标列表
  options = [];

  mounted() {
    this.clsOptions = this.clsOptions.map(item => {
      let disabled = false;
      if (item.value == 'rank') {
        disabled = !this.isClsRankEnable;
      }
      return { ...item, disabled };
    });
    this.grdOptions = this.grdOptions.map(item => {
      let disabled = false;
      if (item.value == 'rank') {
        disabled = !this.isGrdRankEnable;
      }
      return { ...item, disabled };
    });

    if (!this.isRule) {
      this.ruleOptions = [];
    }

    if (this.onlyLv) {
      this.clsOptions = [];
      this.grdOptions = [];
      this.ruleOptions = [];
      this.scoreOptions = this.scoreOptions.filter(item => item.value == 'lv');
    }

    this.options = [...this.scoreOptions, ...this.ruleOptions, ...this.clsOptions, ...this.grdOptions];
    let ruleLabels = this.ruleOptions.map(item => item.label);
    let scoreLabels = this.scoreOptions.map(item => item.label);
    this.checkedOptions = [...scoreLabels, ...ruleLabels];

    // 初始化指标列表
    if (this.indicatorList.length > 0) {
      this.indicator.list = this.indicatorList;
      if (this.defaultIndicators.length > 0) {
        this.indicator.checkList = [...this.defaultIndicators];
      } else {
        // 如果没有默认指标，默认全选
        this.indicator.checkList = this.indicator.list.map(item => item.value || item.label);
      }
      this.indicator.checkAll = this.indicator.checkList.length === this.indicator.list.length;
    }
  }

  // 导出成绩单
  async exportScoreCard() {
    let role = '';
    if (!UserRole.isOperation) {
      const { year, campusCode } = this.$sessionSave.get('reportDetail');
      const map = await UserRole.utils.getRoleSubjectClassMap(
        year,
        campusCode,
        this.$sessionSave.get('reportType') == 'school' ? true : false
      );
      role = JSON.stringify(map);
    }
    const params: any = {
      examId: this.$sessionSave.get('reportDetail').examId,
      role: role,
      source: this.filterData.source,
      withExamId: this.withExamId,
      abPaper: this.filterData.abPaper,
      v: this.$sessionSave.get('reportDetail').v,
    };

    // 添加指标筛选参数
    if (this.indicatorList.length > 0) {
      params.filter = this.indicator.checkList.filter(item => this.indicatorList.some(t => t.value == item)).join(',');
    }

    const urlSearch = new URLSearchParams(params);
    let url = process.env.VUE_APP_KKLURL + `/pexam/_/exportScoreCard?${urlSearch.toString()}`;
    window.open(url);
  }

  // 导出学生成绩单
  async exportStuScoreCard() {
    if (this.checkedOptions.length == 0) {
      this.$message.warning('请先勾选指标！');
      return;
    }

    let card = {
      '': [],
      cls: [],
      grd: [],
    };

    card[''] = this.getOptionValueMap([...scoreOptions, ...ruleOptions]);
    card.cls = this.getOptionValueMap(clsOptions);
    card.grd = this.getOptionValueMap(grdOptions);

    let apiUrl = docTypeOptions.find(item => item.value == this.docType).url;

    const params = {
      examId: this.$sessionSave.get('reportDetail').examId,
      subjectId: this.filterData.subjectId,
      clzId: this.filterData.classId,
      card: JSON.stringify(card),
      abPaper: this.filterData.abPaper,
      v: this.$sessionSave.get('reportDetail').v,
    };
    const urlSearch = new URLSearchParams(params as any);
    let url = process.env.VUE_APP_KKLURL + apiUrl + `?${urlSearch.toString()}`;
    window.open(url);
  }

  getOptionValueMap(options) {
    let arr = [];
    this.checkedOptions.forEach(item => {
      let obj = options.find(t => t.label == item);
      if (obj) arr.push(obj.value);
    });
    return arr;
  }

  handleCheckAllChange(val) {
    let cardOptions = this.options.filter(item => !item.disabled).map(item => item.label);
    this.checkedOptions = val ? cardOptions : [];
    this.isIndeterminate = false;
  }

  handleCheckedCardsChange(value) {
    let checkedCount = value.length;
    this.checkAll = checkedCount === this.options.length;
    this.isIndeterminate = checkedCount > 0 && checkedCount < this.options.length;
  }

  // 指标全选切换
  handleIndicatorCheckAllChange(value) {
    this.indicator.checkList = value ? this.indicator.list.map(item => item.value || item.label) : [];
    this.indicator.isIndeterminate = false;
  }

  // 指标选择改变
  handleIndicatorCheckedChange(value) {
    let checkedCount = value.length;
    this.indicator.checkList = value;
    this.indicator.checkAll = checkedCount === this.indicator.list.length;
    this.indicator.isIndeterminate = checkedCount > 0 && checkedCount < this.indicator.list.length;
  }
}
</script>

<style lang="scss" scoped>
.card-item {
  &:not(:last-of-type) {
    padding-bottom: 20px;
    border-bottom: 1px solid #e5e5e5;
    margin-bottom: 20px;
  }

  .card-item-header {
    display: flex;
    justify-content: space-between;
  }
}
.titleLine {
  position: relative;
  display: flex;
  align-items: center;

  margin-bottom: 10px;

  line-height: 32px;
  font-size: 16px;
  font-weight: bold;
  color: #3f4a54;

  &::before {
    content: '';
    width: 6px;
    height: 24px;
    background: #409eff;
    border-radius: 3px;
    margin-right: 10px;
  }
}
.card-button {
  margin-top: 5px;
}

.exprot-box {
  margin-bottom: 20px;
}

.titleLine-small {
  display: inline-block;
  position: relative;
  height: 32px;
  line-height: 32px;
  font-size: 14px;
  font-weight: bold;
  color: #3f4a54;
  padding-left: 12px;

  &:before {
    content: '';
    width: 4px;
    height: 16px;
    background: #409eff;
    border-radius: 2px;
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    margin: auto;
  }
}

.checkbox-type-group {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  column-gap: 10px;
  row-gap: 10px;
}

.checkbox-box {
  display: flex;

  .checkbox-group {
    width: 120px;
  }

  ::v-deep .el-checkbox {
    margin-bottom: 5px;
  }
}
</style>
