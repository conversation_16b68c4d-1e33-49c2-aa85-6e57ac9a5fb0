<template>
  <div class="exam-mk-container">
    <bread-crumbs :title="'阅卷设置'"></bread-crumbs>

    <!-- 头部选项卡 -->
    <el-tabs v-model="pageType" type="card" @tab-click="changePageType">
      <el-tab-pane v-if="isCompositeSubj()" label="综合科拆分" name="compositeSetting"></el-tab-pane>
      <el-tab-pane label="客观题设置" name="objectSetting"></el-tab-pane>
      <el-tab-pane label="主观题设置" name="subjectSetting" v-if="subjectQuesData.length"></el-tab-pane>
      <el-tab-pane label="智批改设置" name="aiSetting" v-if="hasAIPermission"></el-tab-pane>
      <el-tab-pane label="阅卷老师设置" name="teacher" v-if="source == ISOURCE_TYPES.WEB"></el-tab-pane>
    </el-tabs>

    <div class="exam-mk-main">
      <!-- 题目设置区域 -->
      <div class="exam-mk-ques"
        v-if="pageType == 'objectSetting' || pageType == 'subjectSetting' || pageType == 'aiSetting' || pageType == 'compositeSetting'">
        <!-- 试卷标题信息 -->
        <el-row style="line-height: 50px" type="flex" align="middle">
          <span class="exam-title">{{ examName }}</span>
          <div class="exam-category">
            {{ subjectName }}
            <template v-if="abCardType == IAB_CARD_TYPE.abPaper">
              ({{ abCardSheetType == IAB_CARD_SHEEFT_TYPE.aCard ? 'A' : 'B' }}卷)
            </template>
          </div>
          <span v-if="subjectRealName" style="font-size: 14px;">({{ subjectRealName }})</span>
        </el-row>

        <!-- 试卷统计信息 -->
        <el-row class="exam-info" type="flex" align="middle" justify="space-between">
          <span>
            本试卷共<span class="blue-txt">{{ totalData.totalQuesNum }}</span>题：
            客观题<span class="blue-txt">{{ totalData.objectQuesNum }}</span>题，
            <span v-if="totalData.objectScore != totalData.totalScore">
              共<span class="blue-txt">{{ totalData.objectScore }}</span>分；
            </span>
            <span v-if="subjectQuesData.length">
              主观题<span class="blue-txt">{{ totalData.subjectQuesNum }}</span>题，
              共<span class="blue-txt">{{ totalData.subjectScore }}</span>分；
            </span>
            总分<span class="blue-txt">{{ totalData.totalScore }}</span>分
          </span>

          <!-- 功能按钮区 -->
          <div>
            <el-button
              v-if="abCardType == IAB_CARD_TYPE.abPaper && (pageType == 'objectSetting' || pageType == 'aiSetting')"
              type="text" @click="showCopySetDataDialog = true">
              复用{{ abCardSheetType == IAB_CARD_SHEEFT_TYPE.aCard ? 'B' : 'A' }}卷{{ typeConfig[pageType] }}
            </el-button>

            <template v-if="pageType == 'subjectSetting' && correctType == ICORRECT_TYPES.WEB">
              <el-button type="primary" @click="mergeQuesGroup" :disabled="selectedQues.length <= 1 || notOperate">
                合并题块
              </el-button>
              <el-button :disabled="notOperate" type="warning" @click="jumpSetQuesBlock">
                题块框选
              </el-button>
            </template>

            <el-button v-if="abCardType != IAB_CARD_TYPE.abCard && cardType == ICARD_TYPE.THIRD && pageType == 'objectSetting'"  type="text" @click="openSetChoiceOptsNumDialog">
              修改选项个数
            </el-button>

            <el-button v-if="['objectSetting','subjectSetting'].includes(pageType)" type="text"
              :disabled="(pageType == 'subjectSetting' && source == ISOURCE_TYPES.HAND)" @click="openSetAnswerDialog">
              {{ pageType == 'objectSetting' ? objectText : subjectText }}
            </el-button>
          </div>
        </el-row>

        <!-- 综合科拆分设置 -->
        <template v-if="pageType == 'compositeSetting'">
          <div class="table-container composite-container">
            <div class="big-ques" v-for="(item, itemIndex) in compositeSubjectData" :key="`big-ques-${itemIndex}`">
              <el-row class="big-ques-row">
                <el-col :span="8" class="ques-title">{{ item.name }}</el-col>
                <el-col style="visibility: hidden;" :span="4" class="ques-score">{{ item.score || 0 }}分</el-col>
                <el-col :span="12" class="ques-subject">
                  <el-radio-group :disabled="isStartCorrect" v-model="item.subjectId" @change="changeSubject(item)">
                    <el-radio style="margin-right: 0;" label="">
                      按小题设置
                      <span style="margin-left: 30px;color:#606266;cursor: default;">按大题设置：</span>
                    </el-radio>
                    <el-radio v-for="(subj, subjIndex) in compositeSubject" :key="`subj-${subjIndex}`" :label="subj.id">
                      {{ subj.name }}
                    </el-radio>
                  </el-radio-group>
                </el-col>
              </el-row>

              <el-collapse-transition>
                <div class="ques-list" v-show="!item.subjectId">
                  <el-row v-for="(sitem, sitemIndex) in item.data" :key="`sitem-${sitemIndex}`">
                    <el-col :span="8" class="ques-title">{{ sitem.quesNos }}</el-col>
                    <el-col style="visibility: hidden;" :span="6" class="ques-score">{{ sitem.score }}分</el-col>
                    <el-col :span="10" class="ques-subject">
                      <el-radio-group :disabled="isStartCorrect" v-model="sitem.subjectId"
                        @change="changeSubject(sitem)">
                        <el-radio v-for="(subj, subjIndex) in compositeSubject" :key="`subj-sm-${subjIndex}`"
                          :label="subj.id">
                          {{ subj.name }}
                        </el-radio>
                      </el-radio-group>
                    </el-col>
                  </el-row>
                </div>
              </el-collapse-transition>
            </div>
          </div>
        </template>

        <!-- 题目表格 -->
        <template v-else>
          <div class="ai-container" v-if="pageType == 'aiSetting'">
            <ai-setting :isReadonly="isStartCorrect" :hasStyle="isEnglishSubj()" :quesInfo="quesInfo" :paperNo="paperNo" :mainPaperNo="mainPaperNo" :correctType="correctType" @saveAICorrectQue="editPaper"></ai-setting>
          </div>
          <div class="table-container" v-else>
            <el-table
              :data="pageType == 'objectSetting' ? objectQuesData : subjectQuesData"
              class="exam-ques-table" :key="pageType" @selection-change="handleSelectionChange"
              :row-class-name="tableRowClassName">

              <!-- 选择列 -->
              <el-table-column v-if="pageType == 'subjectSetting' && correctType == ICORRECT_TYPES.WEB" type="selection" width="55"
                :selectable="checkSelectable">
              </el-table-column>

              <!-- 题目名称列 -->
              <el-table-column prop="quesName" label="题目名称">
                <template slot-scope="scope">
                  <el-tooltip :content="scope.row.quesName" placement="top">
                    <span class="showOverTooltip">
                      {{ scope.row.quesName }}
                      <div class="square" v-if="pageType == 'aiSetting'">
                        <span class="square-word">AI</span>
                      </div>
                    </span>
                  </el-tooltip>
                  <span v-if="scope.row.isChooseDo" style="color: #409eff">（选做题{{ scope.row.chooseName.join(',')
                    }}）</span>
                </template>
              </el-table-column>

              <!-- 题号列 -->
              <el-table-column :label="pageType == 'objectSetting' ? '序号-题号' :'题号'" :width="pageType == 'subjectSetting' ? '300' : '200'">
                <template slot-scope="scope">
                  <span v-if="pageType == 'objectSetting'" style="width: 25px;display: inline-block;text-align: right;">
                    {{ scope.$index + 1 }}-
                  </span>
                  <el-input :disabled="notOperate && pageType != 'objectSetting'" v-model="scope.row.quesNos" style="width: 80%; height: 32px"
                    @change="changeInput(scope.row)">
                  </el-input>
                  <el-button v-if="scope.row.isMerge" style="margin-left: 8px" :disabled="notOperate" type="text"
                    @click="cancelMerge(scope.row, scope.$index)">
                    取消合并
                  </el-button>
                </template>
              </el-table-column>

              <!-- 拆分列 -->
              <el-table-column v-if="pageType == 'subjectSetting' && correctType == ICORRECT_TYPES.WEB" prop="isSplit" label="拆分"
                width="100">
                <template slot-scope="scope">
                  <template
                    v-if="scope.row.isMerge || scope.row.isChooseDo || (!scope.row.isSplit && scope.row.threeLevel) || scope.row.scanMode != IQUES_SCAN_MODE.NORMAL">
                    — —
                  </template>
                  <template v-else>
                    <el-button v-if="scope.row.isSplit" :disabled="notOperate" type="text"
                      @click="cancelSplit(scope.row, scope.$index)">
                      取消拆分
                    </el-button>
                    <el-button v-if="!scope.row.isSplit" :disabled="notOperate" type="text"
                      @click="openSetScoreDialog(scope.row, scope.$index, true)">
                      拆分
                    </el-button>
                  </template>
                </template>
              </el-table-column>

              <!-- 题型列 -->
              <el-table-column label="题型" :width="pageType == 'objectSetting' ? '150' : '150'">
                <template slot-scope="scope">
                  <el-select v-if="scope.row.typeId == IQUES_TYPE.choice || scope.row.typeId == IQUES_TYPE.singleChoice" v-model="scope.row.typeId"
                    placeholder="请选择" style="width: 96px; height: 32px"
                    @change="changeQuesType(scope.row)">
                    <el-option v-for="item in objectQuesType" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                  </el-select>
                  <span v-else>
                    {{ getQuesTypeName(Number(scope.row.typeId)) }}
                  </span>
                </template>
              </el-table-column>

              <!-- 答案列 -->
              <el-table-column prop="createTime" label="答案" v-if="pageType == 'objectSetting'">
                <template slot-scope="scope">
                  <!-- 单选、多选 -->
                  <template v-if="scope.row.typeId == IQUES_TYPE.choice || scope.row.typeId == IQUES_TYPE.singleChoice">
                    <el-button v-for="idx of Number(scope.row.optionCount)" :key="`choice-${idx}`" class="choice-btn"
                      :type="scope.row.answer.indexOf(String.fromCharCode(idx + 64)) > -1 ? 'primary' : 'default'"
                      @click="changeAnswer(scope.$index, String.fromCharCode(idx + 64), scope.row.typeId)">
                      {{ String.fromCharCode(idx + 64) }}
                    </el-button>
                  </template>

                  <!-- 判断题 -->
                  <template v-if="scope.row.typeId == IQUES_TYPE.judge">
                    <el-button
                      :type="scope.row.answer == String.fromCharCode(65) ? 'primary' : 'default'"
                      @click="changeAnswer(scope.$index, String.fromCharCode(65), scope.row.typeId)">
                      {{ scope.row.judgeType == 1 ? '√' : scope.row.judgeType == 3 ? 'A' : 'T' }}
                    </el-button>
                    <el-button
                      :type="scope.row.answer == String.fromCharCode(66) ? 'primary' : 'default'"
                      @click="changeAnswer(scope.$index, String.fromCharCode(66), scope.row.typeId)">
                      {{ scope.row.judgeType == 1 ? 'x' : scope.row.judgeType == 3 ? 'B' : 'F' }}
                    </el-button>
                  </template>
                </template>
              </el-table-column>

              <!-- 分值列 -->
              <el-table-column prop="score" label="分值" width="150">
                <template slot-scope="scope">
                  <el-input v-if="!scope.row.isTemp"
                    :disabled="(notOperate && pageType != 'objectSetting') || (pageType == 'subjectSetting' && source == ISOURCE_TYPES.HAND) || (scope.row.scanMode != IQUES_SCAN_MODE.NORMAL && isStartCorrect)" v-model="scope.row.score"
                    style="width: 86px; height: 32px" @input="checkScore(scope.$index, 'score')"
                    @change="scoreChange(scope.row, scope.$index)">
                  </el-input>
                  <template v-else>
                    {{ scope.row.score }}
                  </template>
                </template>
              </el-table-column>

              <!-- 给分步骤列 -->
              <el-table-column v-if="pageType == 'subjectSetting' && correctType == ICORRECT_TYPES.WEB" label="给分步骤"
                show-overflow-tooltip="true">
                <template slot-scope="scope">
                  <template v-if="scope.row.isTemp">
                    — —
                  </template>
                  <el-button v-else-if="scope.row.isMerge || scope.row.isSplit" :disabled="notOperate" type="text"
                    @click="openSetScoreDialog(scope.row, scope.$index, scope.row.isSplit)">
                    {{ getScoreText(scope.row) }}
                  </el-button>
                  <el-button v-else :disabled="notOperate" type="text"
                    @click="openSetScoreDialog(scope.row, scope.$index, false)">
                    {{ scope.row.scoreStep == 1 ? '设置' : `步长${scope.row.scoreStep}分` }}
                  </el-button>
                </template>
              </el-table-column>

              <!-- 漏选分值列 -->
              <el-table-column label="漏选分值" v-if="pageType == 'objectSetting'">
                <template slot-scope="scope">
                  <template v-if="scope.row.typeId == IQUES_TYPE.choice">
                    <el-input v-if="scope.row.ruleType == 0" v-model="scope.row.halfScore"
                      style="width: 80px; height: 32px; margin-right: 10px"
                      @input="checkScore(scope.$index, 'halfScore')">
                    </el-input>
                    <span v-if="scope.row.ruleType == 1" class="rule-type">新高考</span>
                    <span v-if="scope.row.ruleType == 2" class="rule-type">自定义</span>
                    <span v-if="scope.row.ruleType == 3" class="rule-type">断句题</span>
                    <el-button type="text" @click="openSetHalfScoreDialog(scope.row, scope.$index)">编辑</el-button>
                  </template>
                  <template v-else>
                    <span>----</span>
                  </template>
                </template>
              </el-table-column>

              <!-- AI答案列 -->
              <el-table-column width="400" v-if="pageType == 'aiSetting'">
                <template #header>
                  答案<span style="color:#b5b5b5">(若有多个答案，请用"/"隔开)</span>
                </template>
                <template slot-scope="scope">
                  <el-input :disabled="notOperate" style="height: 32px" @input="scope.row.answer = scope.row.answer.replace(/[\t\n\r]/g, '')" v-model="scope.row.answer">
                  </el-input>
                </template>
              </el-table-column>

              <!-- 特殊题型列 -->
              <el-table-column prop="specialTypeId" label="特殊题型" width="150">
                <template slot-scope="scope">
                  <el-select :disabled="notOperate && pageType != 'objectSetting'" v-model="scope.row.specialTypeId" placeholder="请选择"
                    style="width: 96px; height: 32px" @change="changeSpecialType(scope.row)">
                    <el-option v-if="hasWriting && scope.row.typeId == IQUES_TYPE.subject" label="作文题" :value="ISPECIAL_QUES_TYPE.essay">
                    </el-option>
                    <el-option label="附加题" :value="ISPECIAL_QUES_TYPE.appendix">
                    </el-option>
                    <el-option v-if="scope.row.scanMode == IQUES_SCAN_MODE.NORMAL" label="送分题" :value="ISPECIAL_QUES_TYPE.sendScore">
                    </el-option>
                    <el-option v-if="scope.row.scanMode == IQUES_SCAN_MODE.NORMAL" label="0分题" :value="ISPECIAL_QUES_TYPE.zeroScore">
                    </el-option>
                    <el-option label="无" :value="ISPECIAL_QUES_TYPE.none">
                    </el-option>
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column v-if="pageType == 'subjectSetting' && hasAIPermission" prop="scanMode" label="AI智批" width="150">
                <template slot-scope="scope">
                  <el-select v-model="scope.row.scanMode" placeholder="请选择" :disabled="isStartCorrect"  style="width: 96px; height: 32px">
                    <el-option label="无" :value="IQUES_SCAN_MODE.NORMAL"></el-option>
                    <template v-if="isFill(scope.row)">
                      <el-option v-if="hasAIFill(scope.row)" label="填空题智批" :value="IQUES_SCAN_MODE.AI_FILL"></el-option>
                      <el-option v-if="!hasAIFill(scope.row) && scope.row.scanMode == IQUES_SCAN_MODE.AI_FILL" label="(已到期) 填空题智批" :value="IQUES_SCAN_MODE.AI_FILL" disabled></el-option>
                    </template>
                    <template v-if="isSubject(scope.row)">
                      <el-option v-if="hasAISubject(scope.row)" label="简答题智批" :value="IQUES_SCAN_MODE.AI_SUBJECT"></el-option>
                      <el-option v-if="!hasAISubject(scope.row) && scope.row.scanMode == IQUES_SCAN_MODE.AI_SUBJECT" label="(已到期) 简答题智批" :value="IQUES_SCAN_MODE.AI_SUBJECT" disabled></el-option>
                    </template>
                    <template v-if="isEssay(scope.row)">
                      <el-option v-if="hasAIEssay(scope.row)" label="作文题智批" :value="IQUES_SCAN_MODE.AI_ESSAY"></el-option>
                      <el-option v-if="!hasAIEssay(scope.row) && scope.row.scanMode == IQUES_SCAN_MODE.AI_ESSAY" label="(已到期) 作文题智批" :value="IQUES_SCAN_MODE.AI_ESSAY" disabled></el-option>
                    </template>
                  </el-select>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </template>

        <!--底部提示与按钮-->
        <div class="exam-ques-area-footer">
          <div 
            v-if="pageType == 'subjectSetting' && correctType == ICORRECT_TYPES.WEB"
            class="warning-tip"
          >
            <i class="el-icon-warning-outline"></i>
            <div class="warning-content">
              <p>温馨提示：合并或拆分题块后请重新进行题块框选。若已开始阅卷，所修改题块涉及题目的已阅任务将被清空，需要重新阅卷。</p>
            </div>
          </div>
          <div class="footer-btn-area" v-if="pageType != 'aiSetting'">
            <el-button v-if="pageType == 'compositeSetting'" type="primary" :disabled="notOperate"
              @click="saveSubjectData">
              保存修改
            </el-button>
            <el-button v-else type="primary" @click="editPaper">
              保存修改
            </el-button>
          </div>
        </div>
      </div>

      <!-- 阅卷老师设置 -->
      <teacher-setting v-else-if="pageType == 'teacher'" :objectQuesData="objectQuesData"
        :examName="examName" :isAIHasAnswer="isAIHasAnswer" :isCompleteSetArea="isCompleteSetArea" @complete-assign="completeAssign">
      </teacher-setting>
    </div>

    <!-- 各种弹窗组件 -->
    <set-score-batch ref="setScoreBatch" :modalVisible="batchFormVisible"
      :titleName="pageType == 'objectSetting' ? objectText : subjectText" :pageType="pageType" :quesInfo="quesInfo"
      :objectQuesData="objectQuesData"
      @confirm-set-object-score="confirmSetObjectScore"
      @confirm-set-score="confirmSetScore" @close-set-score-modal="closeSetScoreModal">
    </set-score-batch>

    <set-ques-group-dialog :modalVisible="isShowQuesGroup" :currentQues="currentQues"
      :currentSmallIndex="currentSmallIndex" :isSplit="isSplit" @confirm-set-group="confirmSetGroup"
      @close-set-group="closeSetGroup">
    </set-ques-group-dialog>

    <set-choice-scores :isShowDialog="showSetDialog" :item="smallModel" v-if="showSetDialog"
      @close-dialog="showSetDialog = false" @update-score-rules="updateRules">
    </set-choice-scores>

    <copy-set-data-dialog v-if="showCopySetDataDialog" :modalVisible="showCopySetDataDialog" :copyPaperNo="copyPaperNo"
      :type="pageType" :quesInfo="quesInfo" @close-copy-set-data="showCopySetDataDialog = false"
      @confirm-copy-set-data="confirmCopySetData">
    </copy-set-data-dialog>

    <set-choice-opts-num-dialog v-if="setChoiceOptsNumDialogVisible" :modalVisible="setChoiceOptsNumDialogVisible"
      :objectQuesData="objectQuesData" :cardInfo="cardInfo.points" :paperNo="paperNo" @close-dialog="setChoiceOptsNumDialogVisible = false"
      @confirm="confirmChoiceOptsNum">
    </set-choice-opts-num-dialog>
  </div>
</template>

<script>
import { mapGetters, mapState } from 'vuex';
import BreadCrumbs from '@/components/Breadcrumbs/index.vue';
import teacherSetting from './teacherSetting.vue';
import {
  getViewPaper,
  updateViewPaper,
  getScanPaperPoints,
  changeViewPaper,
} from '@/service/testbank';
import { refreshPaperInfo } from '@/service/xueban';
import { checkFloat } from '@/utils/number.js';
import setScoreBatch from './modules/setScoreBatch.vue';
import { SPECIAL_QUES } from '@/typings/scan';
import SetQuesGroupDialog from './modules/setQuesGroupDialog.vue';
import copySetDataDialog from './modules/copySetDataDialog.vue';
import { getQueryString, getQuesType, isNullOrUndefined,isSupportMathSubject } from '@/utils';
import { updateProgress,getAIScanSubjectAPI } from '@/service/pexam';
import { getAbVolumeDetail } from '@/service/api';
import setChoiceScores from './modules/setChoiceScores.vue';
import setChoiceOptsNumDialog from './modules/setChoiceOptsNumDialog.vue';
import { IAB_CARD_TYPE, IAB_CARD_SHEEFT_TYPE, ICARD_STATE,IQUES_TYPE,ICORRECT_TYPES,ISOURCE_TYPES,IPROGRESS_TYPE, ICARD_TYPE,IQUES_SCAN_MODE } from '@/typings/card';
import aiSetting from './modules/aiSetting.vue';
import { ISPECIAL_QUES_TYPE } from '@/typings/scan';

export default {
  name: 'mark-paper-index',
  data() {
    return {
      IQUES_TYPE,
      ICARD_TYPE,
      IAB_CARD_TYPE,
      IAB_CARD_SHEEFT_TYPE,
      ISPECIAL_QUES_TYPE,
      ICORRECT_TYPES,
      ISOURCE_TYPES,
      IPROGRESS_TYPE,
      IQUES_SCAN_MODE,
      //页面类型
      pageType: 'objectSetting',
      aiPageType: '',
      //考试id
      examId: '',
      //考试名称
      examName: '',
      //学科
      subjectName: '',
      subjectId: '',
      subjectRealName: '',
      compositeSubject: [],
      //试卷题卡id
      testBankId: '',
      //试卷类别
      source: ISOURCE_TYPES.HAND,
      //作业id
      workId: '',
      //表格数据
      tableData: [],
      //题卡题目集合
      quesInfo: [],
      //题目统计数据
      totalData: {
        //总分
        totalScore: 0,
        //客观题
        objectScore: 0,
        //主观题分数
        subjectScore: 0,
        //总题数
        totalQuesNum: 0,
        //客观题数量
        objectQuesNum: 0,
        //主观题数量
        subjectQuesNum: 0,
      },
      //客观题数据
      objectQuesData: [],
      //主观题数据
      subjectQuesData: [],
      //综合科拆分数据
      compositeSubjectData: [],
      cardInfo:[],
      specialQuesList: SPECIAL_QUES,
      objectText: '批量设置答案和分值',
      subjectText: '批量设置分值',
      //批量设置分值弹窗
      batchFormVisible: false,
      setChoiceOptsNumDialogVisible: false,
      //是否更新过选项数量
      isUpdateChoiceOptsNum: false,
      selectedQues: [],
      //设置题组弹窗
      isShowQuesGroup: false,
      tempSubjectQuesData: [],
      //坐标点
      pointsData: {},
      quesPoints: {},
      //当前大题序号
      bigIndex: null,
      currentQues: {},
      paperNo: getQueryString('paperNo') || '101678',
      mainPaperNo:"",//ab卷主卡
      //批阅类型 1：手阅 2：网阅
      correctType: ICORRECT_TYPES.WEB,
      elTableHeight: 'calc(100%)',
      personalBookId: '',
      parentId: '',
      //当前所处的步骤
      progress: getQueryString('progress') || IPROGRESS_TYPE.cardSetting,
      //完成状态
      progressState: getQueryString('progressState') || '1',
      tips: '当前处于阅卷中状态，更新题目分值后教师按照最新分值批改，已阅任务中若有大于更新后的得分则取满分，支持教师按照最新分值回评！',
      isThreeLevel: false,
      rules: {
        quesNos: [{ required: true, message: '请输入题号', trigger: 'blur' }],
      },
      currentSmallIndex: 0,
      showSetDialog: false,
      smallModel: {},
      //多选题设置给分规则，当前小题
      smallIndex: 0,
      //quesInfo
      initQues: [],
      //是否保存过阅卷教师
      isAssignment: 0,
      isCompleteAssign: false,
      objectQuesType: [
        {
          value: 1,
          label: '多选题',
        },
        {
          value: 8,
          label: '单选题',
        },
      ],
      isSplit: false,
      copyPaperNo: '',
      abCardType: IAB_CARD_TYPE.default,
      relateCardType: ICARD_STATE.default,
      abCardSheetType: IAB_CARD_SHEEFT_TYPE.aCard,
      cardType: ICARD_TYPE.BLANKCARD,
      showCopySetDataDialog: false,
      typeConfig: {
        objectSetting: '答案和分值',
        subjectSetting: '题块和分值',
        aiSetting: '答案和分值',
        compositeSetting: '阅卷任务'
      },
      aiScanSubject:false,
      aiScanFill:false,
    };
  },
  components: {
    BreadCrumbs,
    teacherSetting,
    setScoreBatch,
    SetQuesGroupDialog,
    setChoiceScores,
    copySetDataDialog,
    aiSetting,
    setChoiceOptsNumDialog
  },
  computed: {
    ...mapGetters([]),
    //不可操作
    notOperate() {
      return this.source == ISOURCE_TYPES.WEB && this.isEndCorrect;
    },
    isStartCorrect() {
     return this.progress >= IPROGRESS_TYPE.startCorrect; 
    },
    isEndCorrect() {
     return this.progress >= IPROGRESS_TYPE.endCorrect; 
    },
    //判断客观题是否都设置答案
    isAllHasAnswer() {
      if (this.objectQuesData.length > 0) {
        return !this.objectQuesData.some(item => {
          return item.answer == '';
        });
      } else {
        return true;
      }
    },
    //判断智批题是否都设置答案
    isAIHasAnswer() {
      if(this.subjectQuesData.length > 0){
        return !this.subjectQuesData.some(item => {
          if(item.scanMode == IQUES_SCAN_MODE.AI_FILL){
            const hasNull = item.lineList.some(line => line.answer.toString() == '');
            const scoreMatch = item.lineList.reduce((acc, line) => {
              return acc + Number(line.score);
            }, 0);
            return hasNull || scoreMatch != item.score;
          }
          if(item.scanMode == IQUES_SCAN_MODE.AI_SUBJECT){
            if(isSupportMathSubject(Number(this.subjectId))){
              let ans = item.answer;
              if(Array.isArray(item.answer)){
                ans = item.answer.join('');
              }
              return ans == '' || item.answerType == 'txt' || (this.correctType == ICORRECT_TYPES.WEB && !item.aiCorrectMode);
            }else{
              return item.answer == '' || (this.correctType == ICORRECT_TYPES.WEB && !item.aiCorrectMode);
            }
          }
        })
      }
      return true;
    },
    hasWriting() {
      //作文题  网阅的语文学科  
      return this.isLanguageSubj() && this.correctType == ICORRECT_TYPES.WEB;
    },
    //是否包含智批权限
    hasAIPermission(){
      return this.aiScanFill || this.aiScanSubject
    },
    // 提取公共判断条件
    hasAIBase() {
      return (item)=>{
        return !item.isChooseDo && !item.isSplit && !item.isTemp && !item.isMerge 
        && (item.specialTypeId != ISPECIAL_QUES_TYPE.sendScore && item.specialTypeId != ISPECIAL_QUES_TYPE.zeroScore) 
        && !item.doQuesList?.length && !this.isCompositeSubj();
      }
    },
    isFill(){
      return (item) => {
        return item.typeId == IQUES_TYPE.fill || item.typeId == IQUES_TYPE.fillEva;
      };
    },
    isSubject(){
      return (item) => {
        return !(this.isEnglishSubj() || this.isLanguageSubj()) && item.typeId == IQUES_TYPE.subject;
      };
    },
    isEssay(){
      return (item) => {
        return (this.isEnglishSubj() || this.isLanguageSubj()) && item.typeId == IQUES_TYPE.subject;
      };
    },
    hasAIFill() {
      return (item) => {
        return this.abCardType != IAB_CARD_TYPE.abCard && this.isFill(item) && this.hasAIBase(item) && this.aiScanFill;
      };
    },
    
    hasAISubject() {
      return (item) => {
        return this.isSubject(item) && this.hasAIBase(item) && this.aiScanSubject;
      };
    },
    
    hasAIEssay() {
      return (item) => {
        return this.isEssay(item) && this.hasAIBase(item) && this.aiScanSubject;
      };
    }
  },
  watch: {},
  created() { 
  },
  async mounted() {
    this.init();
    await this.getCardInfo();
    this.pageType =
      this.isCompositeSubj() ? "compositeSetting" : "objectSetting";
    this.getAIScanSubject();
  },
  methods: {
    isLanguageSubj() {
      return this.subjectId == '1' || this.subjectId == '10' || this.subjectId == '24';
    },
    isEnglishSubj() {
      return this.subjectId == '3' || this.subjectId == '12' || this.subjectId == '25';
    },
    isCompositeSubj() {
      //学科id包含- 认定为综合学科
      return this.subjectId.includes('-');
    },
    tableRowClassName({ row, rowIndex }) {
      if (row.isTemp) {
        return 'hide';
      }
      if(((row.typeId == IQUES_TYPE.singleChoice || row.typeId == IQUES_TYPE.choice || row.typeId == IQUES_TYPE.judge) && row.answer == '' ) || row.score === '') {
        return 'waring';
      }
      return '';
    },
    getScoreText(item) {
      let text = "设置";
      if (item.scoreMode == "0") {
        text = "合并给一个分";
      } else if (item.scoreMode == "1") {
        text = "独立小题给分";
      } else if (item.scoreMode == "2") {
        let scorelist = [];
        item.scoreList.forEach((score) => {
          if (score.quesNo.split(',').length > 1) {
            scorelist.push(score.name + '合并给分')
          } else {
            scorelist.push(score.name + '独立给分')
          }
        })
        text = scorelist.join('; ');
      } else if (item.scoreMode == "3") {
        text = `${item.data.length}小题共用切图`;
      } else if (item.scoreMode == "4") {
        text = `${item.data.length}小题分别切图`;
      }
      return text;
    },
    /**
     * @name：初始化页面参数
     */
    init() {
      let data = this.$route.query;
      this.examId = data.examId || '';
      this.examName = data.examName || '';
      this.subjectName = data.subjectName || '';
      this.subjectId = data.subjectId || '';
      this.subjectRealName = data.subjectRealName || '';
      this.testBankId = data.testBankId || '';
      this.workId = data.workId || '';
      this.source = Number(data.source) || ISOURCE_TYPES.HAND;
      this.personalBookId = data.personBookId;
      if (this.isCompositeSubj()) {
        this.subjectId.split('-').forEach((item, index) => {
          this.compositeSubject.push({ id: String(item), name: this.subjectRealName.split('-')[index] })
        })
      }
    },
    /**
     * @name: 切换头部
     */
    changePageType(name) {
      // this.pageType = name;
      this.objectQuesData = [];
      this.subjectQuesData = [];
      this.totalData.totalScore = 0;
      this.totalData.objectScore = 0;
      this.totalData.subjectScore = 0;
      this.totalData.totalQuesNum = 0;
      this.totalData.objectQuesNum = 0;
      this.totalData.subjectQuesNum = 0;
      this.orderByQuesType();
    },
    async getAIScanSubject(){
      const res = await getAIScanSubjectAPI({
        schoolId: this.$sessionSave.get('schoolInfo').id,
        subjectId: this.subjectId,
        workId: this.workId
      })
      if(res.code == 1){
        this.aiScanFill = res.data.aiFill == 1;
        this.aiScanSubject = res.data.aiCorrect == 1;
      }
    },
    /**
     * 完成分配教师
     */
    completeAssign(data) {
      this.isCompleteAssign = data;
    },
    /**
     * @name: 根据testBankId 获取题卡详情
     */
    async getCardInfo() {
      let result = await getViewPaper({
        paperNo: this.paperNo,
        personalBookId: this.personalBookId,
        type: 1
      });
      if (result && result.code == 1) {
        this.correctType = result.data.correctType;
        this.isAssignment = result.data.isAssignment;
        this.isCompleteSetArea = result.data.isCheckBox;
        this.abCardType = result.data.abCardType;
        this.relateCardType = result.data.relateCardType;
        this.abCardSheetType = result.data.abCardSheetType;
        this.cardInfo = JSON.parse(result.data.cardInfo);
        this.cardType = this.cardInfo.cardType;
        this.copyPaperNo = result.data.abPaperNo.replace(new RegExp(`,?${this.paperNo},?`), '');
        if(this.abCardType == IAB_CARD_TYPE.abPaper){
          this.mainPaperNo = result.data.mainPaperNo;
        }
        this.quesInfo = [];
        let quesJson = result.data.quesInfo;
        if (this.$isJson(quesJson)) {
          let quesInfo =
            result.data.teamInfo != ''
              ? JSON.parse(result.data.teamInfo)
              : JSON.parse(result.data.quesInfo);
          //去除非作答区
          quesInfo = quesInfo.filter(item => {
            return item.typeId != IQUES_TYPE.noAnswerArea;
          });
          let initQuesInfo = JSON.parse(quesJson).filter(item => {
            return item.typeId != IQUES_TYPE.noAnswerArea;
          });
          this.initQues = await this.handleQues(initQuesInfo);
          // this.orderInitQues();
          this.quesInfo = await this.handleQues(quesInfo);
          this.quesInfo.forEach(item => {
            item.data.forEach(ite => {
              this.$set(ite, 'halfScore', ite.halfScore || item.halfScore);
            });
          });
          this.mergeTeam2Ques();
          // console.log('初始获取题目信息', this.quesInfo);
          // console.log('原始题目信息', this.initQues);
          this.orderByQuesType();
          this.setQuesUuid();
        }
      }
    },
    mergeTeam2Ques() {
      let compData = JSON.parse(JSON.stringify(this.initQues));
      let _quesInfos = JSON.parse(JSON.stringify(this.quesInfo))
      //同步teaminfo的学科数据到quesinfo
      _quesInfos = _quesInfos.filter(item => {
        return !item.isMerge && !item.isTemp
      })
      _quesInfos.forEach(item => {
        item.data = item.data.filter(sitem => {
          return !sitem.isMerge && !item.isTemp
        })
      })
      compData.forEach((bques, bindex) => {
        const { subjectId, score, answer,answerType,updatedAt,scoringCriteria, points,lineList, quesNos, typeId, specialTypeId,scanMode } = _quesInfos[bindex];
        bques.subjectId = subjectId;
        bques.score = score;
        bques.answer = answer;
        bques.answerType = answerType || "";
        updatedAt && (bques.updatedAt = updatedAt);
        bques.scoringCriteria = scoringCriteria || "";
        bques.points = points;
        bques.lineList = lineList;
        bques.quesNos = quesNos;
        bques.typeId = typeId;
        bques.specialTypeId = specialTypeId;
        bques.scanMode = scanMode;
        
        delete bques.isHide;
        bques.data?.forEach((sques, sindex) => {
          const { subjectId, score, answer,answerType,updatedAt,scoringCriteria,aiCorrectMode,isArbitration,beyondScore,isCheckScore, points,lineList, quesNos, typeId, specialTypeId,scanMode } = _quesInfos[bindex].data[sindex];
          sques.subjectId = subjectId;
          sques.score = score;
          sques.answer = answer;
          sques.answerType = answerType || "";
          updatedAt && (sques.updatedAt = updatedAt);
          sques.scoringCriteria = scoringCriteria || "";
          sques.aiCorrectMode = aiCorrectMode;
          sques.isArbitration = isArbitration ?? true;
          sques.beyondScore = 2;
          sques.isCheckScore = false;
          sques.points = points;
          sques.lineList = lineList;
          sques.quesNos = quesNos;
          sques.typeId = typeId;
          sques.specialTypeId = specialTypeId;
          sques.scanMode = scanMode;
          delete sques.isHide;
          sques.data?.forEach((qs, qsindex) => {
            const { subjectId, score, answer,answerType,updatedAt,scoringCriteria,aiCorrectMode,isArbitration,beyondScore,isCheckScore, points,lineList, quesNos, typeId, specialTypeId,scanMode } = _quesInfos[bindex].data[sindex].data[qsindex];
            qs.subjectId = subjectId || sques.subjectId;
            qs.score = score || sques.score;
            qs.answer = answer || sques.answer;
            qs.answerType = answerType || sques.answerType;
            updatedAt && (qs.updatedAt = updatedAt);
            qs.scoringCriteria = scoringCriteria || sques.scoringCriteria;
            qs.aiCorrectMode = aiCorrectMode;
            qs.isArbitration = isArbitration ?? true;
            qs.beyondScore = 2;
            qs.isCheckScore = false;
            qs.points = points || sques.points;
            qs.lineList = lineList || sques.lineList;
            qs.quesNos = quesNos || sques.quesNos;
            qs.typeId = typeId || sques.typeId;
            qs.specialTypeId = specialTypeId || sques.specialTypeId;
            qs.scanMode = scanMode || sques.scanMode;
            delete qs.isHide;
          });
        });
      });
      this.compositeSubjectData = compData;
    },
    handleQues(quesInfo) {
      let data = [];
      quesInfo.forEach((item, index) => {
        let bigObj = {
          Index: !item.Index ? index : item.Index,
          data: [],
          halfScore: item.halfScore,
          id: item.id,
          name: item.name,
          quesNos: item.quesNos,
          quesScore: item.quesScore,
          score: item.score,
          type: item.type,
          typeId: Number(item.typeId),
          isMerge: item.isMerge || false,
          isSplit: item.isSplit || false,
          isHide: item.isHide || false,
          scoreList: item.scoreList,
          firstSmallId: item.data[0].id,
          points: item.points || [],
          lineList: item.lineList || [],
          scoreMode: item.scoreMode || 0,
          threeLevel: item.threeLevel || false,
          judgeType: item.judgeType || '',
          specialTypeId: item.specialTypeId || ISPECIAL_QUES_TYPE.none,
          scanMode: item.scanMode || IQUES_SCAN_MODE.NORMAL,
          scoreStep: item.scoreStep || 1,
          ruleType: item.ruleType || 0,
          subjectId: item.subjectId || '',
          // pId: item.pId || "",
          doQuesList: item.doQuesList || [],
        };
        item.data.forEach((ite, smallindex) => {
          let smallObj = {
            id: ite.id,
            name: ite.name,
            quesNo: ite.quesNo,
            quesNos: ite.quesNos,
            score: ite.score,
            type: ite.type,
            typeId: Number(ite.typeId),
            optionCount: ite.optionCount || 4,
            answer: ite.answer || '',
            answerType: ite.answerType || "",
            aiCorrectMode : ite.aiCorrectMode,
            isArbitration : ite.isArbitration ?? true,
            beyondScore : ite.beyondScore || 2,
            isCheckScore : ite.isCheckScore || false,
            scoringCriteria: ite.scoringCriteria || "",
            specialTypeId: ite.specialTypeId || ISPECIAL_QUES_TYPE.none,
            scanMode: ite.scanMode || IQUES_SCAN_MODE.NORMAL,
            scoreStep: ite.scoreStep || 1,
            quesName: ite.quesName,
            points: ite.points,
            lineList: ite.lineList || [],
            isMerge: ite.isMerge || false,
            isSplit: ite.isSplit || false,
            isHide: ite.isHide || false,
            scoreList: ite.scoreList,
            threeLevel: ite.threeLevel || false,
            firstSmallId: (ite.data && ite.data.length > 0 && ite.data[0].id) || '',
            scoreMode: ite.scoreMode || 0,
            halfScore: ite.halfScore,
            ruleType: ite.ruleType || 0,
            rules: ite.rules || [],
            subjectId: ite.subjectId || '',
            // pId: ite.pId || "",
            isChooseDo: ite.isChooseDo,
            chooseIds: ite.chooseIds,
            chooseName: ite.chooseName,
            targetIds: ite.targetIds,
            doCount: ite.doCount,
          };
          ite.updatedAt && (smallObj.updatedAt = ite.updatedAt);
          ite.data &&
            ite.data.forEach(sq => {
              sq.bigIndex = item.Index;
              sq.smallIndex = smallindex;
              sq.answer = sq.answer || "";
              sq.answerType = sq.answerType || "";
              sq.updatedAt && (sq.updatedAt = sq.updatedAt);
              sq.aiCorrectMode = sq.aiCorrectMode,
              sq.isArbitration = sq.isArbitration ?? true,
              sq.beyondScore = sq.beyondScore || 2,
              sq.isCheckScore = sq.isCheckScore || false,
              sq.scoringCriteria = sq.scoringCriteria || "";
              sq.specialTypeId = sq.specialTypeId || ISPECIAL_QUES_TYPE.none;
              sq.scanMode = sq.scanMode || IQUES_SCAN_MODE.NORMAL;
              sq.scoreStep = sq.scoreStep || 1;
              sq.parentId = sq.parentId ? sq.parentId : ite.id;
              // sq.parentData = JSON.parse(JSON.stringify(ite));
              sq.ruleType = sq.ruleType || ite.ruleType || 0;
              sq.rules = sq.rules || ite.rules || [];
              sq.halfScore = sq.halfScore || ite.halfScore;
              sq.typeId = Number(sq.typeId);
              sq.subjectId = sq.subjectId || '';
              // sq.pId = sq.pId || "";
              // sq.quesNos = sq.quesNos;
              sq.subjectId = sq.subjectId || ite.subjectId || '';
            });
          if (ite.data) smallObj.data = ite.data;
          bigObj.data.push(smallObj);
        });
        data.push(bigObj);
      });
      return data;
    },
    orderInitQues(isOrder = true) {
      this.initQues.forEach((item, index) => {
        let obj = item;
        // 题组
        if (obj.isMerge) {
          //二级题型且合并题组
          let ids = obj.data.map(item => item.id);
          let quesNos =
            item.quesNos != '' ? item.quesNos : item.data.map(ite => ite.quesNos).join(',');
          let subItem = {
            data: obj.data,
            bigIndex: obj.Index,
            isMerge: true,
            scoreMode: obj.scoreMode || 0,
            quesName: obj.name,
            quesNos: quesNos,
            quesNo: obj.data[0].quesNo,
            score: obj.score,
            type: obj.type,
            typeId: Number(obj.typeId),
            ids: ids,
            specialTypeId: obj.specialTypeId || ISPECIAL_QUES_TYPE.none,
            scanMode: obj.scanMode || IQUES_SCAN_MODE.NORMAL,
            scoreStep: obj.scoreStep || 1,
            firstSmallId: obj.data[0].id,
            points: obj.points || [],
            lineList: obj.lineList || [],
            quesScore: obj.quesScore || 0,
          };
        } else {
          item.data.forEach(subItem => {
            //三级题型
            if (subItem.data && subItem.data.length > 0) {
              subItem.threeLevel = true;
              subItem.bigIndex = item.Index;
              subItem.firstSmallId = subItem.data[0].id;
              subItem.type = obj.type;
              subItem.quesNo = subItem.data[0].quesNo;
              if (subItem.isMerge) {
                this.$set(subItem, 'quesName', item.name);
              } else {
                subItem.data.forEach(sq => {
                  this.$set(sq, 'quesName', item.name);
                  this.$set(sq, 'bigIndex', item.Index);
                  this.$set(sq, 'threeLevel', true);
                  if (item.judgeType) this.$set(sq, 'judgeType', item.judgeType);
                });
              }
              this.$set(item, 'threeLevel', true);
            } else {
              this.$set(item, 'quesNo', (item.data.length > 0 && item.data[0].quesNo) || '');
              this.$set(item, 'threeLevel', false);
              this.$set(subItem, 'threeLevel', false);
              this.$set(subItem, 'quesName', item.name);
              this.$set(subItem, 'bigIndex', item.Index);
              if (item.judgeType) this.$set(subItem, 'judgeType', item.judgeType);
              // if (!subItem.specialTypeId) this.$set(subItem, 'specialTypeId', 0);
              // if (!subItem.scoreStep) this.$set(subItem, 'scoreStep', 1);
            }
          });
        }
      });
    },
    /**
     * @name:将题目分离主、客观题
     */
    orderByQuesType(isOrder = true) {
      // console.log("%c主客观分离", "color:#409eff;font-weight:bold");
      this.quesInfo.forEach((item, index) => {
        let obj = JSON.parse(JSON.stringify(item));
        // 题组
        if (obj.isMerge) {
          //二级题型且合并题组
          let ids = obj.data.map(item => item.id);
          let quesNos =
            item.quesNos != '' ? item.quesNos : item.data.map(ite => ite.quesNos).join(',');
          let subItem = {
            data: obj.data,
            bigIndex: obj.Index,
            isMerge: true,
            scoreMode: obj.scoreMode || 0,
            quesName: obj.name,
            quesNos: quesNos,
            quesNo: obj.data[0].quesNo,
            score: obj.score,
            type: obj.type,
            typeId: Number(obj.typeId),
            ids: ids,
            specialTypeId: obj.specialTypeId || ISPECIAL_QUES_TYPE.none,
            scanMode: obj.scanMode || IQUES_SCAN_MODE.NORMAL,
            scoreStep: obj.scoreStep || 1,
            firstSmallId: obj.data[0].id,
            points: obj.points || [],
            lineList: obj.lineList || [],
            quesScore: obj.quesScore || 0,
            subjectId: obj.subjectId
          };
          if (obj.scoreList?.length) {
            subItem.scoreList = obj.scoreList;
          }
          this.subjectQuesData.push(subItem);
          // if(!subItem.isChooseDo || (subItem.isChooseDo && subItem?.targetIds?.includes(subItem.id))){
          this.totalData.totalScore = (this.totalData.totalScore*100 + Number(subItem.score)*100)/100;
          // }
          this.totalData.totalQuesNum++;
          this.totalData.subjectQuesNum++;
          this.totalData.subjectScore = (this.totalData.subjectScore*100 + Number(subItem.score)*100)/100;
        } else {
          item.data.forEach(subItem => {
            //三级题型
            if (subItem.data && subItem.data.length > 0 && !subItem.isSplit) {
              let ids = subItem.data.map(item => item.id);
              subItem.threeLevel = true;
              subItem.bigIndex = item.Index;
              subItem.firstSmallId = subItem.data[0].id;
              subItem.type = obj.type;
              subItem.ids = ids;
              subItem.quesNo = subItem.data[0].quesNo;
              if (subItem.isMerge) {
                this.$set(subItem, 'quesName', item.name);
                this.subjectQuesData.push(subItem);
                this.totalData.totalScore = (this.totalData.totalScore*100 + Number(subItem.score)*100)/100;
                this.totalData.totalQuesNum++;
                this.totalData.subjectQuesNum++;
                this.totalData.subjectScore = (this.totalData.subjectScore*100 + Number(subItem.score)*100)/100;
              } else {
                subItem.data.forEach(sq => {
                  if (sq.isHide) return;
                  // this.$set(sq, 'parentData', JSON.parse(JSON.stringify(subItem)));
                  this.$set(sq, 'quesName', item.name);
                  this.$set(sq, 'bigIndex', item.Index);
                  this.$set(sq, 'threeLevel', true);
                  if (item.judgeType) this.$set(sq, 'judgeType', item.judgeType);
                  let typeId = Number(sq.typeId);
                  let isObj = this.$isObjective(typeId);
                  this.totalData.totalScore = (this.totalData.totalScore*100 + Number(sq.score)*100)/100;
                  this.totalData.totalQuesNum++;
                    if (isObj) {
                      if (isOrder) {
                        this.objectQuesData.push(sq);
                      }
                      this.totalData.objectQuesNum++;
                      this.totalData.objectScore = (this.totalData.objectScore*100 + Number(sq.score)*100)/100;
                    }
                    if (!isObj) {
                      if (isOrder) {
                        this.subjectQuesData.push(sq);
                      }
                      this.totalData.subjectQuesNum++;
                      this.totalData.subjectScore = (this.totalData.subjectScore*100 + Number(sq.score)*100)/100;
                    }
                });
              }
              this.$set(item, 'threeLevel', true);
            } else {
              if (subItem.isHide) return;
              this.$set(item, 'quesNo', (item.data.length > 0 && item.data[0].quesNo) || '');
              this.$set(item, 'threeLevel', false);
              this.$set(subItem, 'threeLevel', subItem.isSplit);
              this.$set(subItem, 'quesName', item.name);
              this.$set(subItem, 'bigIndex', item.Index);
              if (item.judgeType) this.$set(subItem, 'judgeType', item.judgeType);
              if (!subItem.specialTypeId) this.$set(subItem, 'specialTypeId', ISPECIAL_QUES_TYPE.none);
              if (!subItem.scanMode) this.$set(subItem, 'scanMode', IQUES_SCAN_MODE.NORMAL);
              if (!subItem.scoreStep) this.$set(subItem, 'scoreStep', 1);
              if (!subItem.isChooseDo || (subItem.isChooseDo && subItem?.targetIds?.includes(subItem.id))) {
                this.totalData.totalScore = (this.totalData.totalScore*100 + Number(subItem.score)*100)/100;
              }
              this.totalData.totalQuesNum++;
              let typeId = Number(subItem.typeId);
              let isObj = this.$isObjective(typeId);
                if (isObj) {
                  if (Array.isArray(subItem.answer)) {
                    subItem.answer = subItem.answer[0];
                  }
                  if (isOrder) {
                    this.objectQuesData.push(subItem);
                  }
                  this.totalData.objectQuesNum++;
                  this.totalData.objectScore = (this.totalData.objectScore*100 + Number(subItem.score)*100)/100;
                }
                if (!isObj) {
                  if (isOrder) {
                    this.subjectQuesData.push(subItem);
                  }
                  //如果是拆分题目 需要添加小题
                  if (subItem.isSplit && subItem.scoreMode == 4) {
                    subItem.data.forEach(sq => {
                      this.$set(sq, 'quesName', item.name);
                      this.$set(sq, 'bigIndex', item.Index);
                      this.$set(sq, 'threeLevel', true);
                      this.$set(sq, 'isTemp', true);
                      this.subjectQuesData.push(sq);
                    });
                  }
                  this.totalData.subjectQuesNum++;
                  if (!subItem.isChooseDo || (subItem.isChooseDo && subItem?.targetIds?.includes(subItem.id))) {
                    this.totalData.subjectScore = (this.totalData.subjectScore*100 + Number(subItem.score)*100)/100;
                  }
                }
              
            }
          });
        }
      });

      this.subjectQuesData.sort((a, b) => a.quesNo - b.quesNo);
      console.log('quesInfo', this.quesInfo);
      // console.log('题目初始客观题', this.objectQuesData);
      // console.log("题目初始主观题", this.subjectQuesData);
    },
    setQuesUuid() {
      this.quesInfo.forEach((item, index) => {
        this.$set(item, 'tempId', index);
      });
    },
    /**
     * @name: 改变单选题答案
     */
    changeAnswer(index, answer, typeId) {
      let sourceData =
        this.pageType == 'objectSetting' ? this.objectQuesData : this.subjectQuesData;
      //单选和判断
      if (typeId == IQUES_TYPE.judge || typeId == IQUES_TYPE.singleChoice) {
        sourceData[index].answer = answer;
      } else if (typeId == IQUES_TYPE.choice) {
        if (sourceData[index].ruleType == 2) {
          sourceData[index].ruleType = 0;
          sourceData[index].rules = [];
        }
        //多选
        let answerArr = [];
        if (sourceData[index].answer) {
          if(typeof sourceData[index].answer == "object"){
            if(sourceData[index].answer.toString() != ""){
              answerArr = sourceData[index].answer;
            }
          }else{
            answerArr = sourceData[index].answer.split(/,|/);
          }
        }
        if (answerArr.includes(answer)) {
          answerArr.splice(answerArr.indexOf(answer), 1);
        } else {
          answerArr.push(answer);
        }
        sourceData[index].answer = answerArr.join(',');
      }
    },
    /**
     * @name: 分值检测
     */
    checkScore(index, key) {
      let sourceData;
      if(this.pageType == 'objectSetting'){
        sourceData = this.objectQuesData;
      } else{
        sourceData = this.subjectQuesData;
      }
      let res = checkFloat(sourceData[index][key]);
      if (res > 100) {
        this.$message({
          message: '分值最大不超过100分!',
          type: 'error',
          duration: 1000,
        });
        res = 100;
      }
      sourceData[index][key] = res || 0;
      if (sourceData[index].isMerge) {
        this.quesInfo.forEach(item => {
          if (sourceData[index].ids.includes(item.firstSmallId)) {
            item.score = sourceData[index][key];
          }
        });
      }
      //多选题自定义规则
      if (sourceData[index].typeId == IQUES_TYPE.choice) {
        if (sourceData[index].ruleType == 2) {
          sourceData[index].ruleType = 0;
          sourceData[index].rules = [];
        }
      }
      this.changePageType();
      // this.orderByQuesType();
    },
    scoreChange(data, index) {
      if (data.isMerge || data.isSplit) {
        //分值修改完成后弹出分值设置框
        this.openSetScoreDialog(data, index, data.isSplit);
      }
      if (data.isChooseDo) {
        //选做题同步更新对应题目数据
        this.quesInfo.forEach(item => {
          item.data.forEach(small => {
            if (small.chooseIds?.join(',') == data.chooseIds?.join(',')) {
              this.$set(small, 'score', data.score);
            }
          })
        });
      }
    },
    /**
     * @name:打开设置分值
     */
    openSetHalfScoreDialog(row, index) {
      this.smallModel = row;
      this.showSetDialog = true;
    },
    /**
     * @name:修改规则
     */
    updateRules(data) {
      const targetId = data[0].id;
      const targetRules = data[0].rules;
      const targetRuleType = data[0].ruleType;

      this.quesInfo.forEach(item => {
        if (item.data) {
          const targetItem = item.data.find(ite => ite.id === targetId);
          if (targetItem) {
            this.$set(targetItem, 'ruleType', targetRuleType);
            this.$set(targetItem, 'rules', targetRules);
          }
          item.data.forEach(small => {
            //三级题型
            if (small.data) {
              const targetItem = small.data.find(ite => ite.id == targetId);
              if (targetItem) {
                this.$set(targetItem, 'ruleType', targetRuleType);
                this.$set(targetItem, 'rules', targetRules);
              }
            }
          });
        }
      });
      this.changePageType();
    },
    changeQuesType(data) {
      const matchedItem = this.objectQuesType.find(item => item.value == data.typeId);
      if (matchedItem) {
        data.type = matchedItem.label;
        data.answer = '';
        if (matchedItem.value == IQUES_TYPE.choice) {
          this.$set(data, 'halfScore', '');
        }
      }
    },
    isSubjectiv(typeid) {
      return ![IQUES_TYPE.choice, IQUES_TYPE.judge, IQUES_TYPE.singleChoice].includes(Number(typeid));
    },
    checkPointsComplete(ques) {
      if (this.isSubjectiv(ques.typeId)) {
        if (!ques.points || !ques.points.length) {
          return false;
        } else {
          return true
        }
      }
      return true;
    },
    /**
     * @name: 获取题目框选是否完成
     */
    getQuesBlockComplete() {
      let isComplete = this.isCompleteSetArea;
      this.quesInfo.forEach(bq => {
        if (bq.isMerge) {
          isComplete && (isComplete = this.checkPointsComplete(bq))
        } else if (bq.isSplit && bq.scoreMode == 3) {
          isComplete && (isComplete = this.checkPointsComplete(bq))
        } else {
          if (bq.data) {
            bq.data.forEach(sq => {
              if (sq.isMerge) {
                isComplete && (isComplete = this.checkPointsComplete(sq))
              } else if (sq.isSplit && sq.scoreMode == 3) {
                isComplete && (isComplete = this.checkPointsComplete(sq))
              } else {
                if (sq.data) {
                  sq.data.forEach(qs => {
                    if (qs.isMerge) {
                      isComplete && (isComplete = this.checkPointsComplete(qs))
                    } else if (qs.isSplit && qs.scoreMode == 3) {
                      isComplete && (isComplete = this.checkPointsComplete(qs))
                    } else if (!qs.isHide) {
                      isComplete && (isComplete = this.checkPointsComplete(qs))
                    }
                  });
                } else if (!sq.isHide && (!sq.isChooseDo || this.cardType == 1 || (sq.isChooseDo && sq.targetIds.includes(sq.id)))) {
                  isComplete && (isComplete = this.checkPointsComplete(sq))
                }
              }
            });
          } else if (!bq.isHide) {
            isComplete && (isComplete = this.checkPointsComplete(bq))
          }
        }
      });
      return isComplete;
    },
    /**
     * @name:保存修改
     */
    async editPaper() {
      this.isCompleteSetArea = this.getQuesBlockComplete();
      let isObjectPage = this.pageType == 'objectSetting' || this.pageType == 'aiSetting';
      if (isObjectPage || this.correctType == ICORRECT_TYPES.HAND) {
        this.saveQuesInfo();
      }
      else if (this.correctType == ICORRECT_TYPES.WEB && this.isCompleteSetArea) {
        this.saveEditPaper()
      } else {
        this.$confirm('为提升阅卷体验，请确认题块框选。', '提示', {
          confirmButtonText: '去确认',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then(async () => {
            this.jumpSetQuesBlock()
          })
          .catch(() => {
          });
      }
    },

    saveSubjectData() {
      this.$confirm('修改题目学科会<span style="color:red;font-size:18px;">重置所有阅卷设置数据</span>，确定修改吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: true
      })
        .then(async () => {
          this.quesInfo = JSON.parse(JSON.stringify(this.compositeSubjectData));
          this.saveQuesInfo();
        })
        .catch(() => {
        });

    },

    saveEditPaper() {
      //开始阅卷后
      if (this.progress == IPROGRESS_TYPE.startCorrect && this.progressState == 1) {
        this.openTips();
      } else {
        this.saveQuesInfo();
      }
    },
    /**
     * @name:打开提示
     */
    async openTips() {
      let tips = '';
      let result = await this.requestAPI('after');
      if (result && result.code == 1 && result.data == 1) {
        tips =
          '现已开始阅卷，所修改题块涉及题目的已阅任务将被清空，需要重新设置阅卷教师、重新阅卷。确定修改题块吗？';
      } else {
        tips = this.tips;
      }
      this.$confirm(tips, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        this.saveQuesInfo();
      });
    },
    /**
     * @name:保存题目信息
     */
    async saveQuesInfo(tips = true) {
      let result = await this.requestAPI();
      if (result && result.code == 1) {
        tips && this.$message({
          message: '修改成功!',
          type: 'success',
          duration: 1000,
        });
        if(this.isUpdateChoiceOptsNum){
          this.refreshPaperPoints()
        }
        this.mergeTeam2Ques()
        //手阅，纯客观题更新进度
        if (
          (this.source == ISOURCE_TYPES.HAND && this.isAllHasAnswer && this.isAIHasAnswer) ||
          (this.subjectQuesData.length == 0 && this.isAllHasAnswer) ||
          (this.isAllHasAnswer && this.isAIHasAnswer && (this.isAssignment > 0 || this.isCompleteAssign))
        ){
          this.changeProcess(1);
        }else{
          this.changeProcess(0);
        }
      } else {
        tips && this.$message({
          message: result.msg || '保存失败',
          type: 'error',
          duration: 1000,
        });
      }
    },
    refreshPaperPoints(){
      const params = {
        paper_no: this.paperNo,
        refresh_points: true
      };
      refreshPaperInfo(params);
    },

    async checkABTotalScore() {
      if (this.relateCardType == ICARD_STATE.abPaper || this.relateCardType == ICARD_STATE.abPaperTwo) {
        if (!this.copyPaperNo) return;
        let params = { schoolId: this.$sessionSave.get('schoolInfo').id, examId: this.examId, paperNo: this.copyPaperNo }
        const res = await getAbVolumeDetail(params);
        if (this.totalData.totalScore != res.data.totalScore) {
          await this.$confirm('A卷、B卷总分值不一致，将影响学情分析，请确认是否继续保存！', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            closeOnClickModal: false,
            type: 'warning'
          })
        }
      }
    },
    /**
     * @name:请求接口
     */
    async requestAPI(type) {
      await this.checkABTotalScore();
      this.quesInfo.forEach(item => {
        if (!item.isMerge && !item.scoreMode == 0) {
          item.score = 0;
          item.data.forEach(ite => {
            if (ite.threeLevel && !ite.isMerge) {
              ite.score = 0;
              ite.data.forEach(it => {
                ite.score += Number(it.score);
              });
            }
            item.score += Number(ite.score);
          });
        }
      });

      let cardInfo = {
        paperNo: this.paperNo,
        cardInfo: JSON.stringify(this.cardInfo),
        teamInfo: JSON.stringify(this.quesInfo),
        examId: this.examId,
        isCheckBox: this.isCompleteSetArea ? 1 : 0,
      };
      return await (type == 'after'
        ? changeViewPaper({ json: JSON.stringify(cardInfo) })
        : updateViewPaper({ json: JSON.stringify(cardInfo) }));
      // if (type == 'after') {
      //   return await changeViewPaper({ json: JSON.stringify(cardInfo) });
      // } else {
      //   return await updateViewPaper({ json: JSON.stringify(cardInfo) });
      // }
    },
    changeInput(row) {
      if (row.isMerge) {
        this.quesInfo.forEach(item => {
          if (item.Index == row.bigIndex && item.firstSmallId == row.firstSmallId) {
            this.$set(item, 'quesNos', row.quesNos);
          }
        });
      }
    },
    changeSubject(item) {
      if (!item.subjectId) return;
      item.data?.forEach((ques) => {
        ques.subjectId = item.subjectId;
        if (ques.data?.length) {
          ques.data.forEach((sq) => {
            sq.subjectId = item.subjectId;
          })
        }
      })
      if (!item.data?.length && item.isChooseDo) {
        this.compositeSubjectData.forEach(big => {
          if (big.doQuesList.length) {
            //选做题同步更新对应题目数据
            big.data.forEach(small => {
              if (small.chooseIds?.join(',') == item.chooseIds?.join(',')) {
                small.subjectId = item.subjectId;
              }
            })
          }
        });
      }
    },
    /**
     * @name：改变当前的进度
     */
    async changeProcess(state) {
      let params = {
        examId: this.examId,
        progress: IPROGRESS_TYPE.correctSetting,
        personalBookId: this.personalBookId,
        progressState: state,
        schoolId: this.$sessionSave.get('schoolInfo').id,
      };
      await updateProgress(params);
    },
    /**
     * @name:打开批量设置弹窗
     */
    openSetAnswerDialog() {
      this.batchFormVisible = true;
    },
    openSetChoiceOptsNumDialog() {
      this.setChoiceOptsNumDialogVisible = true;
    },
    /**
     * @name:打开设置给分步骤
     */
    openSetScoreDialog(data, index, isSplit = false) {
      this.currentSmallIndex = index;
      this.currentQues = data;
      this.isShowQuesGroup = true;
      this.isSplit = false;
      if (isSplit || data.isSplit) {
        this.isSplit = true;
        // this.currentQues.scoreMode = this.currentQues.scoreMode || 3
      } else {
        this.currentQues.scoreMode = this.currentQues.scoreMode ?? 0
      }
    },
    /**
     * @name：合并题组
     */
    mergeQuesGroup() {
      let sid = "";
      let scoreMode = 0;
      //根据大题名称判断合并题目是否跨大题
      this.selectedQues.forEach((item) => {
        if (sid === "") {
          sid = item.subjectId;
        }
        if (sid != item.subjectId) {
          scoreMode = 1;
        }
      })
      // 合并后的数据
      let tempObj = {
        ids: this.selectedQues.map(item => item.id),
        parentId: [...new Set(this.selectedQues.map(item => item.parentId))],
        quesNos: this.selectedQues.map(item => item.quesNos).join(','),
        quesNo: this.selectedQues[0].quesNo,
        bigIndex: this.selectedQues[0].bigIndex,
        threeLevel: this.selectedQues[0].threeLevel,
        score: this.selectedQues.reduce((acc, item) => Number(acc) + Number(item.score), 0),
      };
      //三级结构
      if (tempObj.threeLevel) {
        this.quesInfo.forEach(ite => {
          if (ite.Index == tempObj.bigIndex) {
            ite.data.forEach(sq => {
              if (sq.data) {
                // sq.data = sq.data.filter(it => !tempObj.ids.includes(it.id));
                sq.quesNo = sq.data.length > 0 && sq.data[0].quesNo;
              }
            });
          }
        });
        //大题下面有三级题型
        let isThreeLevel = this.quesInfo.some(item =>
          item.data.some(ite => ite.threeLevel && item.Index == tempObj.bigIndex)
        );
        let tempQuesInfo = this.quesInfo.filter(
          item =>
            !item.isMerge && item.Index == tempObj.bigIndex && (item.threeLevel || isThreeLevel)
        )[0];
        let bigData = tempQuesInfo?.data.filter(ite => {
          return tempObj.parentId.indexOf(ite.id) != -1;
        })[0];
        if (!bigData) {
          let parentData = null;
          let ques = JSON.parse(JSON.stringify(this.initQues));
          ques.some(big => {
            const foundMedium = big?.data.find(medium => medium.id == tempObj.parentId);
            if (foundMedium) {
              parentData = foundMedium;
              return true;
            }
            return false;
          });
          bigData = parentData;
        }
        let newBigQues = JSON.parse(JSON.stringify(bigData));
        newBigQues.quesNos = tempObj.quesNos;
        newBigQues.data = this.selectedQues;
        newBigQues.isMerge = true;
        //0 合并给分 1 独立小题给分
        newBigQues.scoreMode = scoreMode;
        newBigQues.specialTypeId = ISPECIAL_QUES_TYPE.none;
        newBigQues.scanMode = IQUES_SCAN_MODE.NORMAL;
        newBigQues.typeId = this.selectedQues[0].typeId;
        newBigQues.scoreStep = 1;
        newBigQues.points = [];
        newBigQues.quesNo = tempObj.quesNo;
        newBigQues.score = tempObj.score;
        tempQuesInfo.data.push(newBigQues);
        tempQuesInfo.data = tempQuesInfo.data.filter(ite => {
          return !ite.data || (ite.data && ite.data.length != 0);
        });
        tempQuesInfo.data.sort((a, b) => a.quesNo - b.quesNo);
      } else {
        let tempQuesInfo = this.quesInfo.filter(
          item => item.Index == tempObj.bigIndex && !item.isMerge
        )[0];
        //合并后构建大题数据
        let newBigQues = JSON.parse(JSON.stringify(tempQuesInfo));
        newBigQues.quesNos = tempObj.quesNos;
        newBigQues.data = this.selectedQues;
        newBigQues.isMerge = true;
        //0 合并给分 1 独立小题给分
        newBigQues.scoreMode = scoreMode;
        newBigQues.specialTypeId = ISPECIAL_QUES_TYPE.none;
        newBigQues.scanMode = IQUES_SCAN_MODE.NORMAL;
        newBigQues.typeId = this.selectedQues[0].typeId;
        //步长
        newBigQues.scoreStep = 1;
        newBigQues.points = [];
        newBigQues.score = tempObj.score;
        newBigQues.threeLevel = tempObj.threeLevel;
        this.quesInfo.push(newBigQues);
        this.quesInfo = this.quesInfo.filter(item => item.data.length != 0);
        this.quesInfo.forEach(item => {
          this.$set(item, 'quesNo', (item.data.length > 0 && item.data[0].quesNo) || '');
          this.$set(item, 'firstSmallId', (item.data.length > 0 && item.data[0].id) || '');
        });
      }
      //被合并题目增加标记
      this.quesInfo.forEach(ite => {
        ite.data.forEach((sq) => {
          if (tempObj.ids.includes(sq.id)) {
            sq.isHide = true
          }
          if (sq.data) {
            sq.data.forEach((site) => {
              if (tempObj.ids.includes(site.id)) {
                site.isHide = true
              }
            })
            let list = sq.data.filter((obj) => {
              return !obj.isHide
            })
            if (list.length == 0 && !sq.isMerge) {
              sq.isHide = true
            }
          }
        })
        let list = ite.data.filter((obj) => {
          return !obj.isHide
        })
        if (list.length == 0) {
          ite.isHide = true
        }
      });
      this.quesInfo.sort((a, b) => a.quesNo - b.quesNo);
      this.changePageType();
      this.bigIndex = null;
      //选取题目临时记录
      let temSelectedQues = JSON.parse(JSON.stringify(this.selectedQues))
      this.$nextTick(() => {
        let quesno = temSelectedQues[0].quesNo;
        let newQues;
        let newIndex;
        //获取题目列表中匹配合并题目的元素
        this.subjectQuesData.forEach((item, index) => {
          if (item.quesNo == quesno) {
            newQues = item;
            newIndex = index
          }
        });
        if (newQues) {
          //自动弹开分数设置
          this.openSetScoreDialog(newQues, newIndex)
        }
      })
    },
    changeSpecialType(ques) {
      this.quesInfo.forEach(item => {
        if (item.firstSmallId == ques.firstSmallId) {
          this.$set(item, 'specialTypeId', ques.specialTypeId);
        }
        if (item.doQuesList.length && ques.isChooseDo) {
          //选做题同步更新对应题目数据
          item.data.forEach(small => {
            if (small.chooseIds?.join(',') == ques.chooseIds?.join(',')) {
              this.$set(small, 'specialTypeId', ques.specialTypeId);
            }
          })
        }
      });
    },
    /**
     * @name:取消合并
     */
    cancelMerge(item, index) {
      let bigIndex = item.bigIndex;
      let needCancelQues = item.data;
      let ids = item.ids;
      let smallId = item.id;
      this.quesInfo = this.quesInfo.filter(it => !(it.isMerge && it.Index == item.bigIndex && it.quesNos == item.quesNos));
      //还原到未合并的数据
      this.quesInfo.forEach(ite => {
        ite.data = ite.data.filter(sq => !(sq.isMerge && sq.quesNo == item.quesNo));
        ite.data.forEach(sq => {
          if (ids.includes(sq.id)) {
            sq.isHide = false
            ite.isHide = false;
            !sq.scoreMode || (sq.scoreMode = 0);
            !ite.scoreMode || (ite.scoreMode = 0);
          }
          if (sq.data) {
            sq.data.forEach((site) => {
              if (ids.includes(site.id)) {
                site.isHide = false
                sq.isHide = false
                !site.scoreMode || (site.scoreMode = 0);
                !ite.scoreMode || (ite.scoreMode = 0);
              }
            })
          }
        })

      });
      if (!item.threeLevel) {
        this.quesInfo.forEach(item => {
          this.$set(item, 'quesNo', (item.data.length > 0 && item.data[0].quesNo) || '');
        });
        this.quesInfo.sort((a, b) => a.quesNo - b.quesNo);
      }
      this.changePageType();
    },
    cancelSplit(item, index) {
      this.quesInfo.forEach((it, index) => {
        it.data.forEach((sitem, sindex) => {
          if (sitem.id == item.id) {
            this.$set(sitem, 'data', null);
            this.$set(sitem, 'isSplit', false);
            this.$set(sitem, 'threeLevel', false);
            this.$set(sitem, 'scoreMode', 0);
          }
        })
      })
      this.changePageType();
    },
    /**
     * @name:确定设置分数
     */
    confirmSetGroup(quesData, index) {
      const foundItem = this.subjectQuesData.find(
        item =>
          quesData.firstSmallId &&
          quesData.firstSmallId != '' &&
          item.firstSmallId == quesData.firstSmallId
      );
      if (foundItem) {
        Object.assign(foundItem, quesData);
      }
      //未合并题组
      if (!quesData.isMerge) {
        let sourceData =
          this.pageType == 'objectSetting' ? this.objectQuesData : this.subjectQuesData;
        sourceData[index]['scoreStep'] = quesData.scoreStep;
      }
      const { firstSmallId, isMerge, isSplit, data, scoreList, score, scoreMode, scoreStep } = quesData;
      if (quesData.isMerge) {
        this.quesInfo.forEach((ques, index) => {
          if (
            quesData.firstSmallId != '' &&
            ques.firstSmallId == quesData.firstSmallId &&
            ques.isMerge == quesData.isMerge
          ) {
            this.quesInfo[index] = {
              ...this.quesInfo[index],
              data,
              score,
              scoreMode,
              scoreStep,
            };
            if (scoreList?.length) {
              this.quesInfo[index].scoreList = scoreList;
            }
          }
        });
      } else if (quesData.isSplit) {
        this.quesInfo.forEach((item, index) => {
          item.data.forEach((sitem, sindex) => {
            if (sitem.id == quesData.id) {
              this.quesInfo[index].data[sindex] = {
                ...sitem,
                data,
                score,
                scoreMode,
                scoreStep,
                isSplit
              };
            }
          })
        })
      }
      this.changePageType();
      this.isShowQuesGroup = false;
    },
    /**
     * @name:关闭设置分数
     */
    closeSetGroup() {
      if (this.isSplit) {
        this.currentQues.scoreMode = this.currentQues.scoreMode || 0;
      }
      this.isShowQuesGroup = false;
    },
    confirmSetObjectScore(data) {
      this.objectQuesData.forEach((item, index) => {
        item.score = data[index].score !== "" ? data[index].score : item.score;
        item.answer = data[index].answer;
      });
      this.batchFormVisible = false;
    },
    /**
     * @name:批量设置分数
     * @param {*} data
     */
    confirmSetScore(data,pageType) {
      data.forEach(item => {
        item.score = 0;
        //答案
        let tempAnswerList = [];
        if (this.pageType == 'objectSetting' && item.answers != '') {
          tempAnswerList = item.typeId == IQUES_TYPE.choice ? item.answers.split(',') : item.answers.split(/,|/);
          item.data.forEach((smalll, smallIndex) => {
            tempAnswerList.forEach((answer, answerIndex) => {
              if (smallIndex == answerIndex) {
                if (answer == 'T' && item.typeId == IQUES_TYPE.judge) answer = 'A';
                if (answer == 'F' && item.typeId == IQUES_TYPE.judge) answer = 'B';
                smalll.answer = answer;
              }
            });
          });
        }
        //分数
        item.data.forEach((ite, index) => {
          ite.ruleType = item.ruleType;
          if (isFinite(item.quesScore)) {
            ite.data &&
              ite.data.forEach(it => {
                const qsObj = this.$isObjective(it.typeId);
                if (qsObj == (pageType == 'objectSetting')) {
                  it.score = Number(item.quesScore !== '' ? item.quesScore : it.score);
                }
              });
            const smallObj = this.$isObjective(ite.typeId);
            if (smallObj == (pageType == 'objectSetting')) {
              ite.score = Number(item.quesScore !== '' ? item.quesScore : ite.score);
            }
          }
          if (pageType == 'objectSetting' && (ite.typeId == IQUES_TYPE.choice || ite.typeId == IQUES_TYPE.singleChoice)) {
            //多选题
            this.$set(ite, 'halfScore', item.halfScore || 0)
            ite.data &&
              ite.data.forEach(it => {
                this.$set(it, 'halfScore', item.halfScore || 0)
              });
          }
          //大题分数
          item.score += Number(ite.score);
        });
        //题组
        if (item.isMerge && item.quesScore) {
          item.score = Number(item.quesScore);
        }
        this.quesInfo.forEach((ques, index) => {
          if (ques.id == item.id) {
            this.quesInfo[index] = item;
          }
        });
      });
      this.changePageType();
      this.batchFormVisible = false;
    },
    confirmCopySetData(data) {
      this.quesInfo = data;
      this.changePageType();
    },
    confirmChoiceOptsNum(data){
      this.cardInfo.points = data.cardPonits;
      this.objectQuesData.forEach(item => {
        let question = data.question.find(ques => item.id == ques.id);
          item.optionCount = question.optionCount;
          item.answer = question.answer;
      });
      this.isUpdateChoiceOptsNum = true;
      this.setChoiceOptsNumDialogVisible = false;
    },
    /**
     * @name:关闭批量设置分数弹窗
     */
    closeSetScoreModal() {
      this.batchFormVisible = false;
    },
    /**
     * @name:选中行
     * @param {*} val
     */
    handleSelectionChange(val) {
      this.bigIndex = (val.length > 0 && val[0].bigIndex + 1) || null;
      this.parentId = val.length && val[0].parentId;
      this.isThreeLevel = val.length && val[0].threeLevel;
      val.sort((a, b) => a.quesNo - b.quesNo);
      this.selectedQues = val;
    },
    /**
     * @name:设置禁选
     * @param {*} row
     */
    checkSelectable(row) {
      if (row.isSplit || row.isTemp || row.isChooseDo || row.scanMode != IQUES_SCAN_MODE.NORMAL || this.notOperate) {
        return false;
      }
      if (isNullOrUndefined(this.bigIndex) && (isNullOrUndefined(row.isMerge) || !row.isMerge)) {
        return true;
      }
      if (row.isMerge) {
        return false;
      } else {
        return true;
      }
      // if (!isNullOrUndefined(this.bigIndex) && this.bigIndex == row.bigIndex + 1) {
      // if (row.isMerge) {
      //   return false;
      // } else {
      // if (this.isThreeLevel == row.threeLevel) {
      //   return true;
      // } else {
      //   return false;
      // }
      // if (this.parentId) {
      //   if (this.parentId == row.parentId) {
      //     return true;
      //   } else {
      //     return false;
      //   }
      // } else {
      //   if (row.parentId) {
      //     return false;
      //   } else {
      //     return true;
      //   }
      // }
      // }
      // } else {
      //   return false;
      // }
      // return (
      //   (this.bigIndex == "" || (this.bigIndex != "" && this.bigIndex == row.bigIndex + 1)) &&
      //   !row.isMerge
      // );
    },
    /**
     * @name:获取题目坐标
     */
    async getScanPaperPoints(data) {
      let result = await getScanPaperPoints({
        paperNo: this.paperNo,
      });
      if (result && result.code == 1) {
        this.quesPoints = {};
        this.pointsData = JSON.parse(result.data);
        this.pointsData.pages.forEach(item => {
          item.forEach(ite => {
            if (ite.question_id) {
              this.quesPoints[ite.question_id] = ite.pos;
            }
          });
        });
        data.forEach(item => {
          item.data.forEach(ite => {
            this.$set(ite, 'points', this.quesPoints[ite.id]);
          });
        });
      }
      return data;
    },
    /**
     * @name:跳转到题块框选
     */
    async jumpSetQuesBlock() {
      // this.$refs.ruleForm.validate(async (valid) => {
      //   if (valid) {
      await this.requestAPI();
      this.$router.push({
        path: 'settingQuesBlock',
        query: {
          paperNo: this.paperNo,
        },
      });
      //   } else {
      //     this.$message({
      //       message: "题号不能为空",
      //       type: "error",
      //       duration: 1000,
      //     });
      //     return false;
      //   }
      // });
    },
    getQuesTypeName(typeId) {
      return getQuesType(typeId);
    },
  },
};
</script>

<style lang="scss" scoped>
.exam-title {
  font-size: 18px;
  font-weight: bold;
  margin-right: 20px;
  color: #161e26;
}

.exam-category {
  margin-right: 15px;
  height: 24px;
  line-height: 24px;
  background: #f0f2f5;
  border-radius: 12px;
  color: #606266;
  text-align: center;
  font-weight: 400;
  font-size: 14px;
  padding: 0 11px;
}

.exam-info {
  font-size: 14px;
  color: #909299;
  line-height: 24px;
  margin-bottom: 10px;

  .blue-txt {
    color: #008dea;
  }
}

.exam-mk-main {
  background: #fff;
  // min-height: calc(100% - 80px);
  // height: 100%;
  border-radius: 2px;
  border: 1px solid #ebeef1;
  border-top: 0px;

  .exam-mk-ques {
    padding: 20px;
    height: 100%;

    .exam-ques-area-footer {
      margin-top: 10px;
      width: 100%;
      height: 54px;
      line-height: 54px;

      // border-top: 1px solid #f3f3f3;
      .footer-btn-area {
        float: right;
        display: flex;
        align-items: center;
        height: 100%;

        .save-btn {
          width: 116px;
          margin-right: 40px;
        }
      }
    }
  }
}
.empty{
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    text-align: center;
    box-sizing: border-box;
    padding: 40px 0;
    font-size: 14px;
    color: #909399;
}

.table-container {
  height: calc(90% - 200px);
  // overflow-y: auto;
}
.ai-container{
  height: calc(100% - 200px);
  // overflow-y: auto;
}

.composite-container {
  overflow: auto;

  .big-ques {
    .big-ques-row {
      height: 36px;
      line-height: 36px;
      background: #f7fafc;
      color: #606266;
      font-weight: bold;
      border-bottom: 1px solid #EBEEF5;

      .ques-title {
        padding-left: 5px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }

    .ques-list {
      .el-row {
        height: 36px;
        line-height: 36px;
        border-bottom: 1px solid #EBEEF5;

        &:hover {
          background: #f7fafc;
        }
      }

      .ques-title {
        padding-left: 20px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }

    .ques-subject {
      text-align: right;
      padding-right: 40px;
    }
  }
}

.square {
  width: 0;
  height: 0;
  border: 12px solid transparent;
  border-top: 13px solid #409eff;
  border-left: 13px solid #409eff;
  z-index: 100;
  border-radius: 5px 0 0 0;
  position: absolute;
  left: 0;
  top: 0;

  .square-word {
    position: absolute;
    left: -13px;
    top: -23px;
    color: #ffffff;
    font-size: 13px;
  }
}

.ai-word {
  color: #409eff;
  margin-left: 10px;
  font-weight: bold;
  position: absolute;
  // bottom: 8px;
  // right: 3px;
  top: -15px;
  left: -9px;
}

.showOverTooltip {
  display: -webkit-box;
  position: relative;
  text-overflow: ellipsis;
  overflow: hidden;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.rule-type {
  margin-right: 15px;
}

.warning-tip {
  display: inline-flex;
  align-items: flex-start;
  padding: 12px 16px;
  background-color: #fff3f3;
  border-left: 4px solid #ff4d4f;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.warning-tip .el-icon-warning-outline {
  color: #ff4d4f;
  font-size: 18px;
  margin-right: 12px;
  margin-top: 2px;
}

.warning-content {
  flex: 1;
}

.warning-content strong {
  color: #ff4d4f;
  font-weight: 600;
  display: block;
  margin-bottom: 4px;
}

.warning-content p {
  color: #ff4d4f;
  margin: 0;
  line-height: 1.6;
  font-size: 14px;
}
</style>
<style lang="scss">
.exam-mk-container {
  .exam-ques-table {
    .choice-btn {
      &.el-button {
        margin-left: 0;
        margin-right: 10px;
      }
    }
  }

  .el-tabs__header {
    margin: unset !important;
  }

  .el-tabs--card>.el-tabs__header .el-tabs__item.is-active {
    background: #fff;
    font-weight: bold;
  }

  .el-tabs__item {
    font-size: 16px !important;
  }

  .el-input__inner {
    height: 32px;
    line-height: 32px;
    border: 1px solid #dcdfe6;
  }

  .exam-mk-ques {
    .el-table thead {
      color: #606266;
    }

    .el-table th.el-table__cell {
      background-color: #f5f7fa !important;
    }

    .el-table th.el-table__cell>.cell {
      padding-left: 20px !important;
      padding-right: 20px !important;
    }

    .el-table .el-table__cell {
      padding: 8px 0 !important;
    }

    .el-table .el-table__row .cell {
      padding-left: 20px !important;
      padding-right: 20px !important;
      line-height: 35px !important;
    }

    .el-table .el-table__row.hide .el-checkbox {
      display: none;
    }
    .el-table .el-table__row.waring {
      background-color: oldlace;
    }
    .el-table .el-table__body tr.waring:hover>td.el-table__cell{
      background-color: oldlace;
    }

    .el-table {
      .el-button {
        width: 32px;
        height: 32px;
        padding: unset !important;
      }
    }

    .el-table__header-wrapper .el-checkbox {
      display: none;
    }
  }
}

.quesNos-ruleForm {
  .el-form-item {
    margin-bottom: 0px !important;
  }
}
</style>