/**
 * Created by PanJiaChen on 16/11/18.
 */

import UserRole from './UserRole';
/**
 * Parse the time to string
 * @param {(Object|string|number)} time
 * @param {string} cFormat
 * @returns {string | null}
 */
export function parseTime(time, cFormat = '{y}-{m}-{d} {h}:{i}:{s}') {
  if (arguments.length === 0 || !time) {
    return null;
  }
  let date;
  if (typeof time === 'object') {
    date = time;
  } else {
    if (typeof time === 'string') {
      if (/^[0-9]+$/.test(time)) {
        // support "1548221490638"
        time = parseInt(time);
      } else {
        // support safari
        // https://stackoverflow.com/questions/4310953/invalid-date-in-safari
        time = time.replace(new RegExp(/-/gm), '/');
      }
    }

    if (typeof time === 'number' && time.toString().length === 10) {
      time = time * 1000;
    }
    date = new Date(time);
  }
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay(),
  };
  const time_str = cFormat.replace(/{([ymdhisa])+}/g, (result, key) => {
    const value = formatObj[key];
    // Note: getDay() returns 0 on Sunday
    if (key === 'a') {
      return ['日', '一', '二', '三', '四', '五', '六'][value];
    }
    return value.toString().padStart(2, '0');
  });
  return time_str;
}

/**
 * @param {number} time
 * @param {string} option
 * @returns {string}
 */
export function formatTime(time, option) {
  if (('' + time).length === 10) {
    time = parseInt(time) * 1000;
  } else {
    time = +time;
  }
  const d = new Date(time);
  const now = Date.now();

  const diff = (now - d) / 1000;

  if (diff < 30) {
    return '刚刚';
  } else if (diff < 3600) {
    // less 1 hour
    return Math.ceil(diff / 60) + '分钟前';
  } else if (diff < 3600 * 24) {
    return Math.ceil(diff / 3600) + '小时前';
  } else if (diff < 3600 * 24 * 2) {
    return '1天前';
  }
  if (option) {
    return parseTime(time, option);
  } else {
    return d.getMonth() + 1 + '月' + d.getDate() + '日' + d.getHours() + '时' + d.getMinutes() + '分';
  }
}

/**
 * @param {string} url
 * @returns {Object}
 */
export function param2Obj(url) {
  const search = decodeURIComponent(url.split('?')[1]).replace(/\+/g, ' ');
  if (!search) {
    return {};
  }
  const obj = {};
  const searchArr = search.split('&');
  searchArr.forEach(v => {
    const index = v.indexOf('=');
    if (index !== -1) {
      const name = v.substring(0, index);
      const val = v.substring(index + 1, v.length);
      obj[name] = val;
    }
  });
  return obj;
}
export function generateUUID() {
  let d = new Date().getTime();
  let random = Math.floor(Math.random() * 0x10000);
  d += random;

  let UUID = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    let r = (d + Math.random() * 16) % 16 | 0;
    d = Math.floor(d / 16);
    return (c == 'x' ? r : (r & 0x3) | 0x8).toString(16);
  });
  return UUID;
}
// cookie存取操作
export const cookieSave = {
  // 设置cookies
  set: function (name, value) {
    let Days = 30;
    let exp = new Date();
    exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000);
    document.cookie = name + '=' + escape(JSON.stringify(value)) + ';expires=' + exp.toGMTString();
  },
  // 读取cookies
  get: function (name) {
    let arr;
    let reg = new RegExp('(^| )' + name + '=([^]*)(|$)');
    if (document.cookie.match(reg)) {
      arr = document.cookie.match(reg);
      let _result = unescape(arr[2]).split(';')[0];
      try {
        _result = JSON.parse(_result);
      } catch (err) {}
      return _result;
    } else {
      return null;
    }
  },
  // 删除cookies
  del: function (name) {
    let exp = new Date();
    exp.setTime(exp.getTime() - 1);
    let cval = this.$cookieSave.get(name);
    if (cval != null) document.cookie = name + '=' + cval + 'expires=' + exp.toGMTString();
  },
};

// 会话缓存读取操作
export const sessionSave = {
  get: function (key) {
    let data = sessionStorage.getItem(key);
    try {
      data = JSON.parse(sessionStorage.getItem(key));
    } catch (err) {
      // console.log(err)
    }
    return data;
  },
  set: function (key, val) {
    val = JSON.stringify(val);
    sessionStorage.setItem(key, val);
  },
};

// 缓存读取操作
export const localSave = {
  get: function (key) {
    let data = localStorage.getItem(key);
    try {
      data = JSON.parse(data);
    } catch (err) {}
    return data;
  },
  set: function (key, val) {
    // 处理浏览器缓存超出限制的问题，自动清除两个班级的容量
    (function () {
      let size = 0;
      for (let item in window.localStorage) {
        if (window.localStorage.hasOwnProperty(item)) {
          size += window.localStorage.getItem(item).length;
        }
      }
      // 计算缓存剩余容量
      let _residue = 5120 - (size / 1024).toFixed(2);
      // console.log(`剩余容量：${_residue}KB`);
      // 当剩余容量少于10kb时,删除两个班级的缓存
      if (_residue < 100) {
        console.log(`缓存容量不足,清除所有缓存`);
        window.localStorage.clear();
        // let _itemIndex = 0;
        // for (let item in window.localStorage) {
        //     if (window.localStorage.hasOwnProperty(item) && (item.indexOf('stundentList_') > 0 || item.indexOf('rankingList_') > 0)) {
        //         _itemIndex++;
        //         if (_itemIndex < 2) {
        //             localStorage.removeItem(item);
        //         } else {
        //             break;
        //         }
        //     }
        // }
      }
    })();
    try {
      val = JSON.stringify(val);
      localStorage.setItem(key, val);
    } catch (err) {}
  },
};

export function guid() {
  var s = [];
  var hexDigits = '0123456789abcdef';
  for (var i = 0; i < 36; i++) {
    s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1);
  }
  s[14] = '4'; // bits 12-15 of the time_hi_and_version field to 0010
  s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1); // bits 6-7 of the clock_seq_hi_and_reserved to 01
  s[8] = s[13] = s[18] = s[23] = '-';

  var uuid = s.join('');
  return uuid;
}

// 截取文件的后缀.
export function get_suffix(filename) {
  let pos = filename.lastIndexOf('.');
  let suffix = '';
  if (pos !== -1) {
    suffix = filename.substring(pos);
  }
  return suffix;
}

export function _debounce(fn, delay = 200) {
  //防抖
  var timer;
  return function () {
    var th = this;
    var args = arguments;
    if (timer) {
      clearTimeout(timer);
    }
    timer = setTimeout(function () {
      timer = null;
      fn.apply(th, args);
    }, delay);
  };
}
// 节流
export function _throttle(fn, interval = 200) {
  var last;
  var timer;
  return function () {
    var th = this;
    var args = arguments;
    var now = +new Date();
    if (last && now - last < interval) {
      clearTimeout(timer);
      timer = setTimeout(function () {
        last = now;
        fn.apply(th, args);
      }, interval);
    } else {
      last = now;
      fn.apply(th, args);
    }
  };
}

export function getByKey(arr, key, value) {
  for (let i = 0; i < arr.length; i++) {
    if (arr[i][key] == value) return arr[i];
  }
  return null;
}
export function getYears() {
  let date = new Date(),
    y = date.getFullYear(),
    M = date.getMonth(),
    d = date.getDate(),
    years = [];
  for (let i = 0; i < 5; i++) {
    let y1 = y - i - 1;
    let y2 = y - i;
    if (Number(`${M + 1}${d < 10 ? `0${d}` : d}`) >= 810) {
      y1 = y - i;
      y2 = y - i + 1;
    }

    years.push({
      id: `${Number(y1.toString().substring(1))}${Number(y2.toString().substring(1))}`,
      name: `${y1}-${y2}学年`,
    });
  }

  years.unshift({ id: '', name: '全部学年' });
  return years;
}

// 睡眠函数
export function sleep(ms) {
  return new Promise(resolve => {
    setTimeout(resolve, ms);
  });
}

// 根据用户id、学校id存储localStorage
export const localSaveUnique = {
  set(key, value) {
    const schoolId = sessionSave.get('schoolInfo').id;
    const userId = sessionSave.get('loginInfo').id;
    localSave.set(`${key}_${schoolId}_${userId}`, value);
  },
  get(key) {
    const schoolId = sessionSave.get('schoolInfo').id;
    const userId = sessionSave.get('loginInfo').id;
    return localSave.get(`${key}_${schoolId}_${userId}`);
  },
};
export function deepClone(data) {
  if (!data) return data;

  return JSON.parse(JSON.stringify(data));
}

// 获取UA参数
export function getUserAgentParam(key) {
  var arr = navigator.userAgent.split(' ');
  var uaValue = '';
  for (var i = 0; i < arr.length; i++) {
    if (arr[i].indexOf(key) !== -1) uaValue = arr[i];
  }
  if (uaValue) {
    return uaValue.split('/')[1];
  } else {
    return '';
  }
}

// 获取大屏版本code
export function getDtVersionCode() {
  return ver2int(getUserAgentParam('dtappversion'));
}

// 版本号转int
export function ver2int(verStr) {
  let num = 0;
  let ipArr = verStr.split('.');
  /**
   * TIP:兼容大屏1.2.x.x版本
   * 旧版1.2.x.x第三位按8位256换算，大屏最高位999，导致向前进了4位，已经大于1.3.x.x版本
   * 新版提高unit2的转换量，保证大版本的转换量
   **/
  let isOld = verStr.indexOf('1.2.') === 0;
  let unit2 = isOld ? 16 : 18;

  num = (Number(ipArr[0]) << 24) + (Number(ipArr[1]) << unit2) + (Number(ipArr[2]) << 8) + Number(ipArr[3]);
  return num;
}

/**
 * @description: 判断给定的图片源是否为 AVIF 格式
 * @param src 图片源路径
 * @returns  true 表示是 AVIF 格式，否则为 false
 */
export function isAvifIMG(src) {
  if (src.includes('image/format,avif')) {
    return true;
  }
  return false;
}

/**
 * @description: 去除 AVIF 格式参数
 * @param src 字符串类型的图片地址
 * @returns 去除 AVIF 格式参数后的图片地址
 */
export function removeAvifParam(src) {
  src = src.replace(/(,|$)image\/format,avif/, '');
  return src;
}

let isSupportAvif;
/**
 * @description: 判断当前浏览器环境是否支持 AVIF 格式的图片
 * @param src AVIF 格式图片的 URL 或 Blob 数据
 * @returns 若支持则返回 true，否则返回 false
 */
export function supportsAvif(src) {
  // if (isSupportAvif !== undefined && isSupportAvif !== null) return isSupportAvif;
  // if (!createImageBitmap) return false;
  // let avifData =
  //   'data:image/avif;base64,AAAAIGZ0eXBhdmlmAAAAAGF2aWZtaWYxbWlhZk1BMUIAAADybWV0YQAAAAAAAAAoaGRscgAAAAAAAAAAcGljdAAAAAAAAAAAAAAAAGxpYmF2aWYAAAAADnBpdG0AAAAAAAEAAAAeaWxvYwAAAABEAAABAAEAAAABAAABGgAAAB0AAAAoaWluZgAAAAAAAQAAABppbmZlAgAAAAABAABhdjAxQ29sb3IAAAAAamlwcnAAAABLaXBjbwAAABRpc3BlAAAAAAAAAAIAAAACAAAAEHBpeGkAAAAAAwgICAAAAAxhdjFDgQ0MAAAAABNjb2xybmNseAACAAIAAYAAAAAXaXBtYQAAAAAAAAABAAEEAQKDBAAAACVtZGF0EgAKCBgANogQEAwgMg8f8D///8WfhwB8+ErK42A=';
  // if (src) avifData = src;
  // const blob = await fetch(avifData).then(r => r.blob());
  // try {
  //   await createImageBitmap(blob);
  //   isSupportAvif = true;
  // } catch (error) {
  //   isSupportAvif = false;
  // }
  return false;
}

/**
 * @description: 获取图片的URL
 * @param {string} src 图片的URL
 * @return {string} 图片URL
 */
export function getImgUrl(src) {
  const isAvif = isAvifIMG(src);
  if (isAvif) {
    const isSupportsAvif = supportsAvif(src);
    if (isSupportsAvif) return src;
    return removeAvifParam(src);
  }
  return src;
}

// 获取转换后的角色列表
export function getToRoles(year, campusCode = '') {
  const currentRoles = sessionSave.get('currentRoles');
  let roles = [];
  if (UserRole.isOperation) {
    roles = [];
  } else if (currentRoles) {
    roles = currentRoles.split(',');
  } else {
    roles = UserRole.utils.getRolesByYear(year, campusCode);
  }
  return roles.map(item => Number(item));
}

// 转换年级为学段
export function coverGrdCodeToPhase(grdCode) {
  let map = {
    1: 3, // 一年级
    2: 3, // 二年级
    3: 3, // 三年级
    4: 3, // 四年级
    5: 3, // 五年级
    6: 3, // 六年级
    7: 4, // 七年级
    8: 4, // 八年级
    9: 4, // 九年级
    10: 5, // 高一
    11: 5, // 高二
    12: 5, // 高三
  };
  return map[String(grdCode)];
}

/**
 * @description: 动态载入js
 * @param {string} moduleName
 * @param {string} src
 * @return {*}
 */
export const loadScript = async (src, moduleName) => {
  return new Promise((resolve, reject) => {
    if (moduleName && window[moduleName]) {
      resolve(null);
      return;
    }

    const script = document.createElement('script');
    script.src = src;
    script.async = true;
    script.onload = () => {
      resolve(null);
    };
    script.onerror = () => {
      console.error('Failed to load script');
      reject();
    };
    document.body.appendChild(script);
  });
};

/**
 * 动态加载样式文件
 * @param {string} url 样式文件URL
 * @returns {Promise} 返回Promise对象
 */
export function loadStyle(url) {
  return new Promise((resolve, reject) => {
    const link = document.createElement('link');
    link.rel = 'stylesheet';
    link.href = url;
    link.onload = resolve;
    link.onerror = reject;
    document.head.appendChild(link);
  });
}

/** 交集 */
export function findIntersection(arr1, arr2) {
  const set = new Set(arr1);
  const intersection = [];
  for (let item of arr2) {
    if (set.has(item)) {
      intersection.push(item);
    }
  }
  return intersection;
}

/** 并集 */
export function findUnion(arr1, arr2) {
  return [...new Set([...arr1, ...arr2])];
}

/** 补集 */
export function findComplement(arr1, arr2) {
  const unionSet = findUnion(arr1, arr2);
  const intersectionSet = findIntersection(arr1, arr2);
  return unionSet.filter(value => !intersectionSet.includes(value));
}

// 是否是CEF环境
export function isCef() {
  return !!window.cef;
}

// 下载文件
export async function downloadFile(url, fileName) {
  return new Promise(async (resolve, reject) => {
    const res = await fetch(url);
    if (res.status == 404) {
      reject({ msg: '下载失败，文件不存在！' });
      return;
    }

    const blob = await res.blob();
    let downUrl = window.URL.createObjectURL(blob);
    let a = document.createElement('a');
    a.href = downUrl;
    if (fileName) a.download = fileName;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(downUrl);
    resolve();
  });
}

// 通过iframe下载文件
export async function downloadFileByIframe(url) {
  return new Promise(async (resolve, reject) => {
    const iframe = document.createElement('iframe');
    iframe.style.display = 'none';
    iframe.src = url;
    iframe.onload = () => {
      document.body.removeChild(iframe);
      resolve();
    };
    iframe.onerror = () => {
      reject();
    };
    document.body.appendChild(iframe);
  });
}

// 从链接获取文件后缀名
export function getFileExtension(url) {
  const path = url.split('/').pop(); // 获取路径中的最后一部分
  const ext = path.split('.').pop(); // 获取文件扩展名
  return ext;
}

// 新增裁剪方法
export function setCroppedImageUrl(source, wScale = 1, hScale) {
  const pattern = /crop,x_(\d+),y_(\d+),w_(\d+),h_(\d+)/;
  const cropMatch = source.match(pattern);
  if (!hScale) {
    hScale = wScale;
  }

  if (cropMatch) {
    const currentParams = {
      x: Number(cropMatch[1]),
      y: Number(cropMatch[2]),
      w: Number(cropMatch[3]),
      h: Number(cropMatch[4]),
    };

    const newX = Math.max(0, Math.round(currentParams.x - currentParams.w * ((wScale - 1) / 2)));
    const newY = Math.max(0, Math.round(currentParams.y - currentParams.h * ((hScale - 1) / 2)));
    const newW = Math.round(currentParams.w * wScale);
    const newH = Math.round(currentParams.h * hScale);
    return source.replace(pattern, `crop,x_${newX},y_${newY},w_${newW},h_${newH}`);
  }
  return source;
}

/**
 * 根据坐标裁剪图片（Canvas版）
 * @param {string[]} imgArr - 图片URL数组
 * @param {Array<{page:number, pos:number[]}>} coords - 坐标数组
 * @param {number} dpi - 分辨率，默认96
 * @returns {Promise<string[]>} 返回切图的base64数组
 */
export async function cropImagesWithCanvas(imgArr, coords, dpi = 96) {
  let pxPerMm = dpi / 25.4;
  const results = [];
  // 等待图片加载的工具函数
  function loadImage(src) {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.crossOrigin = 'anonymous'; // 允许跨域
      img.onload = () => resolve(img);
      img.onerror = reject;
      img.src = src;
    });
  }

  for (const coord of coords) {
    const { page, pos } = coord;
    if (!imgArr[page]) continue;

    const img = await loadImage(imgArr[page]);
    pxPerMm = img.height / (297 * pxPerMm) * pxPerMm;
    const [xMm, yMm, wMm, hMm] = pos;
    const x = Math.round(xMm * pxPerMm);
    const y = Math.round(yMm * pxPerMm);
    const width = Math.round(wMm * pxPerMm);
    const height = Math.round(hMm * pxPerMm);

    // 创建canvas
    const canvas = document.createElement('canvas');
    canvas.width = width;
    canvas.height = height;
    const ctx = canvas.getContext('2d');

    // 裁剪并绘制
    ctx.drawImage(img, x, y, width, height, 0, 0, width, height);

    // 输出 base64
    results.push(canvas.toDataURL('image/png'));
  }

  return results;
}

// 判断是否弱密码
export function checkWeakPassword(password) {
  return /^\d+$/.test(password) || /^[a-zA-Z]+$/.test(password);
}

// 判断密码强度
export function checkPasswordStrength(pwd) {
  if (pwd.length < 8 || pwd.length > 16) {
    return {
      valid: false,
      message: '密码长度必须在8-16位之间',
    };
  }

  if (checkWeakPassword(pwd)) {
    return {
      valid: false,
      message: '密码必须包含字母、数字、特殊符号中的至少两种',
    };
  }

  return {
    valid: true,
    message: '',
  };
}
