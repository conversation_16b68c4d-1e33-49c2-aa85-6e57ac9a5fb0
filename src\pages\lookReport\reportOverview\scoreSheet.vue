<template>
  <div class="score-sheet" v-loading="isLoading">
    <div class="score-header">
      <div class="score-header--left">
        <div class="titleLine">学生小分表</div>
        <el-popover placement="bottom" width="400" trigger="click" popper-class="popover" v-model="isPopoverVisible">
          <el-checkbox-group class="checkbox-type-group" v-model="tempCheckTypeList">
            <el-checkbox
              v-for="item in typeList"
              :key="item.value"
              class="checkbox"
              :label="item.value"
              :disabled="item.disabled"
              >{{ item.label }}</el-checkbox
            >
          </el-checkbox-group>
          <div class="popover-footer">
            <el-button size="small" @click="isPopoverVisible = false">取消</el-button>
            <el-button type="primary" size="small" @click="handleCheckType">确定</el-button>
          </div>
          <el-button class="filtrate" slot="reference" type="text"
            >指标筛选 <i class="el-icon-arrow-down"></i
          ></el-button>
        </el-popover>
      </div>

      <div class="score-header--right">
        <el-input
          class="search__text"
          placeholder="输入考号或姓名搜索"
          v-model="searchValue"
          @keyup.enter.native="listStuDesc"
          clearable
        >
        </el-input>
        <div class="search__icon el-icon-search" slot="append" @click="listStuDesc"></div>
        <el-button class="button export-button" type="primary" @click="isDialogVisible = true">导出</el-button>
      </div>
    </div>

    <el-tooltip placement="bottom-start" v-if="doQuesList.length">
      <div slot="content">
        <div v-html="getDoQuesChoiceTipHtml()"></div>
      </div>
      <span class="mixin-dochoice-tip">{{ getDoQuesChoiceTipText() }}</span>
    </el-tooltip>
    <div v-if="tableData.length">
      <el-table
        :data="tableData"
        stripe
        :header-cell-style="{ fontSize: '14px', color: '#3F4A54', backgroundColor: '#f5f7fa' }"
        style="width: 100%"
        v-drag-table
        v-sticky-table="0"
      >
        <el-table-column align="center" label="考号" prop="stuNo" fixed :min-width="100" :show-overflow-tooltip="true">
        </el-table-column>
        <el-table-column
          align="center"
          label="班级"
          prop="clzName"
          fixed
          :min-width="100"
          :show-overflow-tooltip="true"
        >
        </el-table-column>
        <el-table-column
          align="center"
          label="姓名"
          prop="stuName"
          fixed
          :min-width="100"
          :show-overflow-tooltip="true"
        >
        </el-table-column>
        <el-table-column
          v-if="checkTypeList.includes(1)"
          align="center"
          label="等级"
          fixed
          prop="grdLv"
          :min-width="100"
          :show-overflow-tooltip="true"
        >
        </el-table-column>
        <el-table-column
          v-if="checkTypeList.includes(4)"
          align="center"
          label="得分"
          fixed
          prop="score"
          :min-width="100"
          :show-overflow-tooltip="true"
        >
        </el-table-column>
        <el-table-column
          v-if="checkTypeList.includes(7)"
          align="center"
          label="班级排名"
          fixed
          prop="clzRank"
          :min-width="100"
          :show-overflow-tooltip="true"
        >
          <template #default="scope">
            <span v-if="isClsRankEnable">{{ scope.row.clzRank }}</span>
            <span v-else>
              <el-tooltip effect="dark" content="应相关部门/学校要求，该数据不予展示" placement="top">
                <i class="el-icon-lock"></i>
              </el-tooltip>
            </span>
          </template>
        </el-table-column>
        <el-table-column
          v-if="checkTypeList.includes(10)"
          align="center"
          label="年级排名"
          fixed
          prop="grdRank"
          :min-width="100"
          :show-overflow-tooltip="true"
        >
          <template #default="scope">
            <span v-if="isGrdRankEnable">{{ scope.row.grdRank }}</span>
            <span v-else>
              <el-tooltip effect="dark" content="应相关部门/学校要求，该数据不予展示" placement="top">
                <i class="el-icon-lock"></i>
              </el-tooltip>
            </span>
          </template>
        </el-table-column>

        <!-- 根据指标筛选显示不同类型的列 -->
        <template v-for="item in tableHeader">
          <el-table-column
            v-if="shouldShowColumn(item)"
            align="center"
            :prop="`data[${item.id}].score`"
            :key="item.id"
            :label="item.name"
            :min-width="150"
          >
            <template #header>
              <el-tooltip effect="dark" :content="item.name" placement="top"
                ><span class="ellipsis">
                  {{ item.name }}

                  <span v-if="getDoChoiceQuesCountTextByQuesNo(item.id)">
                    (<span class="mixin-dochoice-text">{{ getDoChoiceQuesCountTextByQuesNo(item.id) }}</span
                    >)
                  </span>
                </span>
              </el-tooltip>
            </template>

            <el-table-column
              v-if="getIsShowScoreColumn(item)"
              align="center"
              :label="`得分【${item.data.score}】`"
              :min-width="105"
              :show-overflow-tooltip="true"
            >
              <template #header>
                <el-tooltip effect="dark" :content="`得分【${item.data.score}】`" placement="top"
                  ><span class="ellipsis">
                    {{ `得分【${item.data.score}】` }}
                  </span></el-tooltip
                >
              </template>
              <template #default="scope">
                <span
                  :style="{
                    color:
                      scope.row.data[item.id].score != item.data.score &&
                      !getIsBigQues(item.id) &&
                      scope.row.data[item.id].score != -1
                        ? 'red'
                        : 'inherit',
                  }"
                >
                  {{ scope.row.data[item.id].score === -1 ? '--' : scope.row.data[item.id].score }}
                </span>
              </template>
            </el-table-column>

            <el-table-column
              v-if="getIsShowAnswerColumn(item)"
              align="center"
              :label="`作答【${item.data.answer}】`"
              :min-width="105"
            >
              <template #header>
                <el-tooltip effect="dark" :content="`作答【${item.data.answer}】`" placement="top"
                  ><span class="ellipsis">
                    {{ `作答【${item.data.answer}】` }}
                  </span></el-tooltip
                >
              </template>
              <template #default="scope" :show-overflow-tooltip="true">
                <span
                  :style="{
                    color:
                      scope.row.data[item.id].score != item.data.score && !getIsBigQues(item.id) ? 'red' : 'inherit',
                  }"
                >
                  {{ scope.row.data[item.id].answer }}
                </span>
              </template>
            </el-table-column>
          </el-table-column>
        </template>
      </el-table>
      <el-pagination
        class="pagination"
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        layout="total, prev, pager, next, sizes"
        :current-page.sync="pagination.page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.pageSize"
        :total="pagination.total"
      >
      </el-pagination>
    </div>
    <no-data v-else></no-data>

    <CheckSubClassDialog
      v-if="isDialogVisible"
      :canIndicator="true"
      :indicatorList="typeList"
      :defaultIndicators="checkTypeList"
      @closed="isDialogVisible = false"
      @confirm="exportStuDesc"
    />
  </div>
</template>

<script lang="ts">
import NoData from '@/components/noData.vue';
import CheckSubClassDialog from './components/CheckSubClassDialog.vue';
import { exportStuDesc, listStuDesc } from '@/service/pstat';
import { Component, Mixins, Prop, Vue, Watch } from 'vue-property-decorator';
import UserRole from '@/utils/UserRole';

import DochoiceMixin from '../mixin/DochoiceMixin.vue';
import { FuncIdEnum, getFuncEnable, indicatorManager } from '@/utils/examReportUtils';

export interface IFilterData {
  classId: string;
  classIds: any[];
  subjectId: string;
  phaseId: number;
  xfId: number;
  subjectName: string;
  abPaper: string;
}

@Component({
  components: {
    NoData,
    CheckSubClassDialog,
  },
})
export default class ScoreSheet extends Mixins(DochoiceMixin) {
  @Prop({
    type: Object,
    default: () => {},
  })
  filterData: IFilterData;

  // 表格数据
  tableData = [];
  // 表格表头
  tableHeader = [];
  // 是否加载
  isLoading: boolean = false;
  // 搜索值
  searchValue: string = '';
  // 分页器
  pagination = {
    page: 1,
    pageSize: 10,
    total: 0,
  };
  // 指标列表
  typeList: Partial<{ label: string; value: number; disabled: boolean }>[] = indicatorManager.scoreSheetIndicatorList;
  // 筛选类型
  checkTypeList = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];
  // 暂存筛选类型
  tempCheckTypeList = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];
  // 是否显示弹出框
  isPopoverVisible: boolean = false;
  // 校排是否可用
  isGrdRankEnable: boolean = true;
  // 班排是否可用
  isClsRankEnable: boolean = true;
  // 是否显示导出弹窗
  isDialogVisible: boolean = false;

  @Watch('filterData', {
    deep: true,
  })
  async updateFilter(val: IFilterData) {
    this.tableData = [];
    this.tableHeader = [];
    this.pagination.page = 1;
    this.listStuDesc();
    this.getPaperChoice(this.reportDetail.examId, this.filterData.subjectId);
  }

  @Watch('isPopoverVisible')
  async updateIsPopoverVisible(val: boolean) {
    this.tempCheckTypeList = JSON.parse(JSON.stringify(this.checkTypeList));
  }

  // 当前报告详情
  get reportDetail(): IExamReportInfo {
    return this.$sessionSave.get('reportDetail');
  }

  getIsShowScoreColumn(item) {
    if (this.getIsBigQues(item.id)) {
      return true;
    } else {
      if (this.$isObjective(item.qType)) {
        return this.checkTypeList.includes(6);
      } else {
        return this.checkTypeList.includes(3);
      }
    }
  }

  getIsShowAnswerColumn(item) {
    if (this.$isObjective(item.qType)) {
      return this.checkTypeList.includes(9) && item.data.answer;
    } else {
      return false;
    }
  }

  // 获取是否大题
  getIsBigQues(id) {
    return id == 'obj' || id == 'sub' || id.includes('t_') || id.includes('b_');
  }

  // 判断是否应该显示列
  shouldShowColumn(item) {
    const itemId = item.id;

    if (this.getIsBigQues(itemId)) {
      // 主客观得分 (2) - 显示客观题(obj)、主观题(sub)列
      if ((itemId === 'obj' || itemId === 'sub') && !this.checkTypeList.includes(2)) {
        return false;
      }
      // 题型得分 (5) - 显示题型列
      if (itemId.includes('t_') && !this.checkTypeList.includes(5)) {
        return false;
      }
      // 大题得分 (8) - 显示大题列
      if (itemId.includes('b_') && !this.checkTypeList.includes(8)) {
        return false;
      }
      return true;
    }

    if (this.$isObjective(item.qType)) {
      // 客观小题分 (6)显示客观小题列  客观题作答 (9)显示客观题得分列
      return this.getIsShowScoreColumn(item) || this.getIsShowAnswerColumn(item);
    } else {
      // 主观小题分 (3) - 显示主观小题列
      return this.getIsShowScoreColumn(item);
    }
    return true;
  }

  mounted() {
    this.checkTypeList = indicatorManager.getIndicator('scoreSheet');
    this.listStuDesc();
    this.getPaperChoice(this.reportDetail.examId, this.filterData.subjectId);
  }

  // 获取学生数据
  async listStuDesc() {
    this.isGrdRankEnable = getFuncEnable({ funcId: FuncIdEnum.QsGrdRank, classId: this.filterData.classId });
    this.isClsRankEnable = getFuncEnable({ funcId: FuncIdEnum.QsClsRank, classId: this.filterData.classId });

    const params = {
      examId: this.reportDetail.examId,
      subjectId: this.filterData.subjectId,
      classId: this.filterData.classId,
      abPaper: this.filterData.abPaper,
      text: this.searchValue,
      type: this.checkTypeList.join(','),
      v: this.reportDetail.v,
      page: this.pagination.page,
      pageSize: this.pagination.pageSize,
    };

    try {
      this.isLoading = true;
      const res = await listStuDesc(params);
      this.tableData = res.data.rows.rows || [];
      this.tableHeader = res.data.rows.qsHead || [];
      this.pagination.page = res.data.page;
      this.pagination.total = res.data.total_rows;
      this.isLoading = false;
    } catch (error) {
      console.error(error);
      this.isLoading = false;
      this.tableData = [];
      this.tableHeader = [];
    }

    this.getTypeList();
  }

  getTypeList() {
    if (!this.isGrdRankEnable) {
      this.typeList = this.typeList.map(item => {
        if (item.value == 10) {
          item.disabled = true;
        }
        return item;
      });
    }
    if (!this.isClsRankEnable) {
      this.typeList = this.typeList.map(item => {
        if (item.value == 7) {
          item.disabled = true;
        }
        return item;
      });
    }
  }

  handleSizeChange(val) {
    this.pagination.pageSize = val;
    this.pagination.page = 1;
    this.listStuDesc();
  }

  handleCurrentChange(val) {
    this.pagination.page = val;
    this.listStuDesc();
  }

  handleCheckType() {
    this.checkTypeList = JSON.parse(JSON.stringify(this.tempCheckTypeList));
    this.isPopoverVisible = false;
    this.listStuDesc();
    indicatorManager.setIndicator('scoreSheet', this.checkTypeList);
  }

  // 导出小分表
  async exportStuDesc({ subjectCheckList, indicatorCheckList }) {
    let role = '';
    if (!UserRole.isOperation) {
      const { year, campusCode } = this.$sessionSave.get('reportDetail');
      const map = await UserRole.utils.getRoleSubjectClassMap(
        year,
        campusCode,
        this.$sessionSave.get('reportType') == 'school' ? true : false
      );
      role = JSON.stringify(map);
    }

    const params: any = {
      examId: this.reportDetail.examId,
      subjectId: subjectCheckList.join(','),
      type: indicatorCheckList.join(','),
      role: role,
      abPaper: this.filterData.abPaper,
      v: this.reportDetail.v,
    };
    const urlSearch = new URLSearchParams(params);
    let url = process.env.VUE_APP_KKLURL + `/pstat/_/export-stu-desc?${urlSearch.toString()}`;
    window.open(url);
    this.isDialogVisible = false;
  }
}
</script>

<style scoped lang="scss">
.score-header {
  display: flex;
  align-items: center;

  &--left {
    display: flex;
    align-items: center;
    flex: 1;
  }

  &--right {
    display: flex;
  }

  .filtrate {
    margin-left: 10px;
  }

  .header__search {
    display: flex;
    width: 240px;
  }

  .search__icon {
    display: flex;
    justify-content: center;
    align-items: center;

    width: 38px;
    font-size: 18px;
    color: #fff;
    background: #409eff;
    border-radius: 0 3px 3px 0;
    outline: none;
    cursor: pointer;
  }

  .button {
    margin-left: 20px;
  }
}

.pagination {
  text-align: center;
  margin-top: 20px;
}

.popover-footer {
  margin-top: 10px;
  text-align: right;
}

.titleLine {
  display: inline-block;
  position: relative;
  height: 54px;
  line-height: 54px;
  font-size: 16px;
  font-weight: bold;
  color: #3f4a54;
  padding-left: 16px;

  &:before {
    content: '';
    width: 6px;
    height: 24px;
    background: #409eff;
    border-radius: 3px;
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    margin: auto;
  }
}

.checkbox-type-group {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  column-gap: 10px;
  row-gap: 10px;
}
</style>
