# 作文设置组件 (EssaySetting)

## 概述

这是一个增强的作文设置组件，支持图片显示区域，提供了完整的表单功能和图片管理功能。

## 新增功能

### 1. 图片显示区域
- 位置固定在右侧，占据30%宽度
- 支持图片上传、预览、删除
- 响应式设计，小屏幕自动换行
- 粘性定位，滚动时保持可见

### 2. 布局优化
- 使用Flexbox布局实现文字环绕效果
- 表单项宽度自动调整（有图片时60%，无图片时100%）
- 完全响应式设计

### 3. 用户体验增强
- 图片加载状态显示
- 图片加载失败占位符
- 拖拽上传支持（可扩展）
- 图片预览功能

## Props

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| aiEssayQuesData | Array | [] | 题目数据数组 |
| isReadonly | Boolean | false | 是否只读模式 |
| hasStyle | Boolean | false | 是否显示作文体裁选择 |
| correctType | Number | ICORRECT_TYPES.WEB | 批改类型 |
| imgList | Array | [] | 图片列表（兼容旧版本） |
| showImages | Boolean | true | 是否显示图片区域 |
| subjectId | String/Number | '' | 学科ID，用于判断是否为英语学科 |

## Events

| 事件名 | 说明 | 参数 |
|--------|------|------|
| saveAICorrectQue | 保存作文设置时触发 | - |

## 使用示例

### 基础使用

```vue
<template>
  <essay-setting
    :aiEssayQuesData="quesData"
    :isReadonly="false"
    :hasStyle="true"
    :showImages="true"
    :subjectId="1"
    @saveAICorrectQue="handleSave"
  />
</template>

<script>
import EssaySetting from './modules/essaySetting.vue';

export default {
  components: {
    EssaySetting
  },
  data() {
    return {
      quesData: [
        {
          id: 1,
          quesNo: 'Q001',
          quesNos: '第1题',
          score: 25,
          way: 1,
          question: '请根据以下材料写一篇英语作文...',
          corrRule: '语法正确，内容完整，逻辑清晰...'
        }
      ]
    };
  },
  methods: {
    handleSave() {
      console.log('保存作文设置');
    }
  }
};
</script>
```

### 只读模式

```vue
<essay-setting
  :aiEssayQuesData="quesData"
  :isReadonly="true"
  :showImages="false"
/>
```

## 样式定制

组件使用SCSS编写，支持以下CSS变量定制：

```scss
// 图片区域宽度
--image-area-width: 30%;

// 表单区域宽度（有图片时）
--form-section-width: 65%;

// 响应式断点
--responsive-breakpoint: 1200px;
```

## 图片功能

### 支持的图片格式
- JPG/JPEG
- PNG
- GIF
- WebP

### 图片大小限制
- 单个文件最大5MB
- 支持多文件上传

### 图片操作
- **上传**: 点击"添加图片"按钮或空状态区域
- **预览**: 点击图片进行预览
- **删除**: 点击图片信息区域的删除按钮

## 响应式设计

| 屏幕宽度 | 布局方式 | 图片区域 | 表单区域 |
|----------|----------|----------|----------|
| > 1200px | 水平布局 | 30%宽度，右侧固定 | 65%宽度 |
| ≤ 1200px | 垂直布局 | 100%宽度，下方显示 | 100%宽度 |

## 兼容性

- Vue 2.x
- Element UI 2.x
- 现代浏览器（IE11+）

## 注意事项

1. **图片存储**: 当前使用本地URL预览，生产环境需要实现实际的上传接口
2. **权限控制**: 通过`isReadonly`属性控制编辑权限
3. **数据绑定**: 图片数据存储在`form.images`数组中
4. **内存管理**: 组件会自动释放blob URL，避免内存泄漏

## 扩展功能

可以通过以下方式扩展组件功能：

1. **拖拽上传**: 在图片区域添加拖拽事件监听
2. **图片编辑**: 集成图片裁剪、旋转功能
3. **批量操作**: 添加批量删除、排序功能
4. **云存储**: 集成OSS、CDN等云存储服务

## 更新日志

### v1.1.0
- 新增图片显示区域
- 优化响应式布局
- 增强用户体验
- 添加完整的错误处理

### v1.0.0
- 基础作文设置功能
- 表单验证
- 作文体裁选择
