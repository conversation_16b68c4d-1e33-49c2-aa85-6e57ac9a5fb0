<template>
  <div class="ai-setting-container">
    <template v-if="fillData.length || subjectData.length || essayData.length">
      <el-tabs v-model="activeName" type="border-card" @tab-click="handleClick">
        <el-tab-pane v-if="fillData.length" label="填空题" name="fill">
          <fillSetting :aiFillQuesData="fillData" :isReadonly="isReadonly" :imgList="imgList" :subjectId="subjectId" :paperNo="paperNo"
            :mainPaperNo="mainPaperNo" @saveAICorrectQue="saveAICorrectQue" />
        </el-tab-pane>
        <el-tab-pane v-if="subjectData.length" label="简答题" name="subject">
          <subjectSetting :aiSubjectQuesData="subjectData" :isReadonly="isReadonly" :correctType="correctType"
            @saveAICorrectQue="saveAICorrectQue" />
        </el-tab-pane>
        <el-tab-pane v-if="essayData.length" label="作文题" name="essay">
          <essaySetting :aiEssayQuesData="essayData" :isReadonly="isReadonly" :imgList="imgList" :subjectId="subjectId"
            :correctType="correctType" @saveAICorrectQue="saveAICorrectQue" />
        </el-tab-pane>
      </el-tabs>
    </template>
    <el-empty description="暂未设置智批题" v-else></el-empty>
  </div>
</template>

<script>
import { ICORRECT_TYPES, IQUES_SCAN_MODE } from '@/typings/card';
import essaySetting from './essaySetting.vue';
import fillSetting from './fillSetting.vue';
import subjectSetting from './subjectSetting.vue';
import { getPaperImages } from '@/service/xueban';
export default {
  name: "ai-setting",
  components: {
    essaySetting,
    fillSetting,
    subjectSetting
  },
  props: {
    quesInfo: {
      type: Array,
      default: () => []
    },
    isReadonly: {
      type: Boolean,
      default: false
    },
    paperNo: {
      type: String,
      default: ''
    },
    mainPaperNo: {
      type: String,
      default: ''
    },
    subjectId: {
      type: String,
      default: ''
    },
    correctType: {
      type: Number,
      default: ICORRECT_TYPES.WEB
    }
  },
  data() {
    return {
      IQUES_SCAN_MODE,
      activeName: "fill",
      fillData: [],
      subjectData: [],
      essayData: [],
      imgList: [],
    };
  },
  computed: {
  },
  async created() {
    await this.getImgUrls();
    this.initData();
    this.activeName = this.fillData.length ? 'fill' : this.subjectData.length ? 'subject' : 'essay';
    
  },
  methods: {
    initData() {
      this.quesInfo.forEach(item => {
        let hasFill = false;
        item.data.forEach(qitem => {
          if (qitem.data?.length) {
            qitem.data.forEach(sqitem => {
              if (sqitem.scanMode == IQUES_SCAN_MODE.AI_FILL) {
                hasFill = true;
              } else if (sqitem.scanMode == IQUES_SCAN_MODE.AI_SUBJECT) {
                this.subjectData.push(sqitem);
              } else if (sqitem.scanMode == IQUES_SCAN_MODE.AI_ESSAY) {
                this.essayData.push(sqitem);
              }
            })
          } else {
            if (qitem.scanMode == IQUES_SCAN_MODE.AI_FILL) {
              hasFill = true;
            } else if (qitem.scanMode == IQUES_SCAN_MODE.AI_SUBJECT) {
              this.subjectData.push(qitem);
            } else if (qitem.scanMode == IQUES_SCAN_MODE.AI_ESSAY) {
              this.essayData.push(qitem);
            }
          }
        });
        if (hasFill) {
          this.fillData.push(item);
        }
      });
    },
    /**
 * @name:获取生成的pdf图片
 */
    async getImgUrls() {
      const params = {
        paper_no: this.mainPaperNo || this.paperNo,
        show_answer: false,
      };
      const res = await getPaperImages(params);
      if (res.code == 1) {
        this.imgList = res.data;
      } else {
        this.imgList = [];
      }
    },
    handleClick(tab, event) {
      console.log(tab, event);
    },
    saveAICorrectQue() {
      this.$emit('saveAICorrectQue');
    },
  }
};
</script>

<style lang="scss" scoped>
.ai-setting-container {
  height: 100%;

  .custom-select {
    width: 100%;
  }

  .score-display {
    font-size: 16px;
    color: #67c23a;
    font-weight: bold;
  }

  .hint-text {
    font-size: 12px;
    color: #909399;
    margin-top: 5px;
  }

  .form-buttons {
    margin-top: 30px;
    text-align: center;
  }

  .el-form-item {
    margin-bottom: 25px;
  }

  .el-textarea {
    ::v-deep .el-textarea__inner {
      border-color: #dcdfe6;
      transition: all 0.3s;

      &:focus {
        border-color: #409eff;
        box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
      }
    }
  }

  .essay-type-radio {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .radio-item {
    display: flex;
    flex-direction: column;
    border: 1px solid #EBEEF5;
    border-radius: 4px;
    padding: 12px 16px;
    transition: all 0.3s;
  }

  .radio-item:hover {
    border-color: #C0C4CC;
    background-color: #F5F7FA;
  }

  .radio-desc {
    margin-top: 8px;
    margin-left: 24px;
    font-size: 13px;
    color: #606266;
    line-height: 1.5;
  }

  /* 当单选框被选中时，高亮显示对应项 */
  .radio-item:has(.el-radio.is-checked) {
    border-color: #409EFF;
    background-color: #ecf5ff;
  }
}
</style>