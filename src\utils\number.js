function trimExtraChar(value, char, regExp, isPositive = false, toFixed = 8) {
  const index = value.indexOf(char);

  if (index === -1) {
    return value;
  }
  // 如果为正数去除开头的减号
  if (isPositive && char === "-" && index === 0) {
    return value.slice(index + 1);
  }

  // 数字中间不能存放减号，如果存在，则截取减号前面的数字
  if (char === "-" && index !== 0) {
    let chars = value.split("");
    chars[index] = "";
    console.log(chars, chars[index]);
    return chars.join("");
    // return value.slice(0, index);
  }

  let v = "";
  if (char === "." && value.indexOf(".") !== -1) {
    v = value.slice(0, index + 1) + value.slice(index).replace(regExp, "").slice(0, toFixed);
  } else {
    v = value.slice(0, index + 1) + value.slice(index).replace(regExp, "");
  }

  return v;
}

export function trimExtraZero(value) {
  value = "" + value;
  let [preVal = "", sufVal = ""] = value.split(".");
  preVal = preVal.replace(/\b(0+)/gi, "");
  preVal = preVal === "-" ? (sufVal === "" ? "0" : "-0") : preVal;
  preVal = preVal === "" ? "0" : preVal;
  if (!sufVal) {
    return preVal;
  }
  return preVal + "." + sufVal;
}

export function formatNumber(value, allowDot, isPositive = false, toFixed = 8) {
  if (allowDot) {
    value = trimExtraChar(value, ".", /\./g, isPositive, toFixed);
  } else {
    value = value.split(".")[0];
  }

  value = trimExtraChar(value, "-", /-/g, isPositive, toFixed);

  const regExp = allowDot ? /[^-0-9.]/g : /[^-0-9]/g;

  return value.replace(regExp, "");
}

export function isDef(val) {
  return val !== undefined && val !== null;
}

/*浮点数检查*/
export function checkFloat(value) {
  value = value
    .toString()
    .replace(/[^\d.]/g, "") // 清除“数字”和“.”以外的字符
    .replace(/\.{2,}/g, ".") // 只保留第一个. 清除多余的
    .replace(".", "$#$")
    .replace(/\./g, "")
    .replace("$#$", ".")
    .replace(/^(\-)*(\d+)\.(\d).*$/, "$1$2.$3"); // 只能输入两个小数
  if (!/\./g.test(value) && value != "") {
    // 以上已经过滤，此处控制的是如果没有小数点，首位不能为类似于 01、02的金额
    return parseFloat(value);
  }
  // 类似于.123这样的以点开始,其余都为数字,且小数点后面多余2位
  if (/^\.(\d+)$/g.test(value) && value.length > 2) {
    return value.substr(0, 2);
  }
  if (value.startsWith('0') && parseFloat(value) > 1) {
    return value.slice(1);
  }
  return value
}
/**
 * 将浮点数取小数点后1位
 */
export function toDecimal(val) {
  var f = parseFloat(val);
  if (isNaN(f)) {
    return;
  }
  f = Math.floor(val * 10) / 10;
  return f;
}

/**
 * 计算平均值（保证结果为0.5的倍数）
*/
export function distributeScore(total, count) {
  let avg = total / count;
  // 四舍到0.5的平均基础值
  let base = Math.floor(avg * 2) / 2; 
  let result = new Array(count).fill(base);

  // 当前总和
  let currentSum = result.reduce((a, b) => a + b, 0);

  // 需要补的差值
  let diff = +(total - currentSum).toFixed(1);

  // 补到最后一位
  result[count - 1] = +(result[count - 1] + diff).toFixed(1);

  return result;
}
