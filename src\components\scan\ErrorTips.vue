<template>
  <div>
    <div class="tips-header">
      <span>异常原因：<span>{{ errorTips[currentError]?.reason }}</span>
        <span class="handle-tips" @click="dialogVisible = true">操作提示</span>
      </span>
      <span><span v-if="currentError == 'objective' || currentError == 'subject'">注：点击蓝框即可进行修正</span>
        <span v-else>
          <el-button style="margin-right: 10px;font-size: 16px;" type="text" v-if="isShowPointHandle" @click="modifyPoint">定位点修改</el-button>
          <el-button style="margin-right: 10px;font-size: 16px;" type="text" v-if="isShowStuNameHandle" @click="modifyPoint">识别手写考号、姓名</el-button>
          <span v-if="examErrorData.length != 0">扫描时间：{{ scanTime.substr(0, 16) || "" }}</span>
        </span>
      </span>
    </div>
    <el-dialog
      title="操作提示"
      v-if="dialogVisible"
      :visible.sync="dialogVisible"
      width="45%"
      :before-close="closeDialog"
      class="tips-dialog"
    >
      <video  
        webkit-playsinline="true"
        x-webkit-airplay="true"
        playsinline="true"
        x5-video-player-type="h5"
        x5-video-player-fullscreen="true"
        controls="controls" autoplay
        preload="auto" 
        :src="errorTips[currentError].video" 
        style="width: 100%;">
        您的浏览器不支持 video 标签
      </video>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="dialogVisible = false"
          >我知道啦</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { ERROR_TIPS, ERRORCARD_TYPE } from "@/typings/scan";

export default {
  props: {
    //当前错误类型
    currentError: {
      type: String,
      default: "",
    },
    //当前错误类型
    examErrorData: {
      type: Array,
      default: () => [],
    },
    //当前错误数量
    errorCount: {
      type: Number,
      default: 0,
    },
    //是否配置识别姓名
    isScanStuName:{
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      errorTips: ERROR_TIPS,
      dialogVisible: false,
      //扫描时间
      scanTime: "",
    };
  },
  watch: {
    currentError(newVal, oldVal) {},
    examErrorData(newVal, oldVal) {
      this.scanTime = (newVal[0] && newVal[0].date_created) || "";
    },
  },
  computed:{
    isShowPointHandle() {
      return this.currentError == 'point' && this.errorCount > 20;
    },
    isShowStuNameHandle() {
      return this.currentError == 'exam' && this.isScanStuName;
    },
  },
  mounted() {
    console.log(this.errorCount)
  },
  methods: {
    modifyPoint() {
      const h = this.$createElement;
      this.$msgbox({
        title: '定位点修改',
        message: h('div', { class: 'custom-ponits-container' }, [
          h('p', { class: 'warn-tip' }, '修改定位点后，需重新识别方可生效，且后续扫描均按照新的定位点进行识别。'),
          h('div', { class: 'handle-row' }, [
            h('span', { class: 'handle-click',
            on:{
                click:()=>{
                  window.open(`/scan-view/view_paper?paper_no=${this.examErrorData[0].card_id}`)
                }
              }
             }, '>>前往定位点修改页面',
             
            )
          ])
        ]),
        showCancelButton: true,
        closeOnClickModal: false,
        confirmButtonText: '重新识别',
        cancelButtonText: '取消',
        customClass: 'page-points-dialog',
        beforeClose: async (action, instance, done) => {
            if (action === 'confirm') {
              this.refreshPaperPoints();
            }
            done();
          },
      });
    },
    refreshPaperPoints(){
      let code;
      if(this.currentError == 'point'){
        code = 67;
      }else if(this.currentError == 'exam'){
        code = 8;
      }
      this.$emit("update-points",code);
    },
    closeDialog() {
      this.dialogVisible = false;
    },
  },
};
</script>

<style lang="scss" scoped>
.tips-header {
  display: flex;
  justify-content: space-between;
  padding-right: 15px;
}
.handle-tips {
  color: #409eff;
  margin-left: 15px;
  cursor: pointer;
}
</style>
<style lang="scss">
.tips-dialog {
  .el-dialog__footer {
    text-align: center;
  }
}
</style>
<style lang="scss">
.page-points-dialog{
  .custom-ponits-container{
    padding: 10px 0;
    .warn-tip {
    display: block;
    color: #f56c6c;
    font-size: 13px;
    margin-bottom: 16px;
    padding: 8px 16px;
    background-color: #fef0f0;
    border-radius: 4px;
    border: 1px solid #fde2e2;

    &::before {
      content: "⚠";
      margin-right: 8px;
    }
    }
    .handle-click{
      color: #409eff;
      cursor: pointer;
    }
  }
}
</style>
