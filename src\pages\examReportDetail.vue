<template>
  <div class="reportDetail" ref="reportDetail" :class="{ desktop: isDesktop }" :key="key">
    <!-- 报告头部 -->
    <div class="reportDetail_top" v-if="!isDesktop">
      <div v-if="$sessionSave.get('reportDetail')" class="flex_1">
        <report-header @back="backToList" @setExamReport="setExamReport"></report-header>
        <div class="tip" v-if="!isCef">
          依据教育部“双减”政策要求，严禁拍照/截图/导出等方式泄露考试成绩、排名等相关信息，违者上报至教育主管部门严肃追责！
        </div>
      </div>
      <div>
        <el-button class="downloadeBtn" type="primary" @click="gotoTarget">设置指标</el-button>
        <el-button class="downloadeBtn" @click="isShowDownReport = true" v-if="downLoadState == 1">下载报告</el-button>
      </div>
    </div>
    <!-- 报告主体 -->
    <div class="reportDetail__main">
      <!-- 左侧菜单 -->
      <div class="left-sub-menu">
        <report-menu ref="reportMenu"></report-menu>
      </div>
      <!-- 右侧内容 -->
      <transition name="slide-fade" mode="out-in">
        <router-view
          v-if="isMenuInit"
          class="right-content"
          ref="routerView"
          id="rightContent"
          :key="$route.path"
        ></router-view>
      </transition>
    </div>

    <!--下载报告-->
    <download-report
      v-if="isShowDownReport"
      :key="key"
      ref="downloadreport"
      @cloase-dialog="isShowDownReport = false"
    ></download-report>
    <!-- 试卷讲评匹配对话框 -->
    <paper-match-dialog v-if="isShowOperationDialog" @closed="isShowOperationDialog = false"></paper-match-dialog>
    <!-- 回到顶部按钮 -->
    <el-backtop class="backtop" target=".scrollContainer" :right="26" :bottom="220">
      <img :src="topIcon" />
    </el-backtop>
  </div>
</template>

<script lang="ts">
import ReportHeader from '@/components/ExamReport/report-header.vue';
import ReportMenu from '@/components/ExamReport/report-menu.vue';
import DownloadReport from '@/components/downloadReport.vue';
import { getExamCache, getSchCfgAPI } from '@/service/pexam';
import { getToRoles, isCef } from '@/utils/index';
import { Component, Provide, Ref, Vue } from 'vue-property-decorator';

import PaperMatchDialog from '@/components/PaperMatchDialog.vue';
import { getExamReportSubjectList, indicatorManager, ScoreStatusManager } from '@/utils/examReportUtils';
import { SchoolSettingType } from './schoolSetting/types';

@Component({
  components: {
    DownloadReport,
    ReportMenu,
    ReportHeader,
    PaperMatchDialog,
  },
})
export default class ExamReportDetail extends Vue {
  @Ref() reportMenu: ReportMenu;
  // 置顶图标
  topIcon = require('@/assets/icon_top.png');
  // 更新key
  key: number = 0;
  // 是否cef环境
  isCef = isCef();
  // 报告下载状态
  downLoadState: number = this.$sessionSave.get('downLoadState');
  // 是否显示下载报告对话框
  isShowDownReport: boolean = false;
  // 是否显示运营对话框
  isShowOperationDialog: boolean = false;
  // 菜单是否初始化
  isMenuInit: boolean = false;
  // 成绩状态管理器
  scoreStatusManager: ScoreStatusManager = null;

  // 是否桌面报告
  get isDesktop() {
    return this.$route.path.includes('/dReport');
  }

  mounted() {
    this.init();
    this.$bus.$on('handle-tool', () => {
      this.isShowOperationDialog = true;
    });
  }

  beforeDestroy() {
    this.$bus.$off('handle-tool');
    document.documentElement.style.zoom = '1';
    this.scoreStatusManager.stopCheck();
  }

  async init() {
    this.scoreStatusManager = new ScoreStatusManager();
    await this.scoreStatusManager.startCheck(this.$sessionSave.get('reportDetail').examId);
    // 更新考试缓存
    await this.updateExamCache();
    // 获取考试学科列表
    await this.getExamSubjectList();
    // 获取是否仅显示等级
    await this.getOnlyLvConfig();
    // 获取指标配置
    await indicatorManager.init();
    // 初始化菜单项
    this.reportMenu.init();
    // 菜单是否初始化
    this.isMenuInit = true;
    // 桌面报告放大zoom
    if (this.isDesktop) {
      document.documentElement.style.zoom = '1.05';
    }
  }

  // 获取考试学科列表
  async getExamSubjectList() {
    const { examId, campusCode, year, gradeName, v } = this.$sessionSave.get('reportDetail');
    let roles = getToRoles(year, campusCode);
    const { roleSubjectList, noRoleSubjectList } = await getExamReportSubjectList({
      examId: examId as any,
      campusCode,
      roles,
      v,
      year,
      gradeName,
      statType: 1,
    });
    this.$sessionSave.set('innerNoRoleSubjectList', noRoleSubjectList);
  }

  // 获取仅显示等级配置
  async getOnlyLvConfig() {
    // 仅显示等级在成绩单中去除组合成绩榜选项、赋分选项
    const res = await getSchCfgAPI({
      schId: this.$sessionSave.get('schoolInfo').id,
      type: SchoolSettingType.ScoreStatisticRule,
      examId: this.$sessionSave.get('reportDetail').examId,
    });
    let onlyLv = 0;
    if (res.data && res.data.jCfg) {
      onlyLv = res.data.jCfg.onlyLv;
    } else {
      onlyLv = 0;
    }
    this.$sessionSave.set('onlyLv', onlyLv);
  }

  // 更新考试缓存
  async updateExamCache() {
    try {
      const res = await getExamCache({
        examId: this.$sessionSave.get('reportDetail').examId,
      });
      this.$sessionSave.set('reportDetail', { ...this.$sessionSave.get('reportDetail'), v: res.data.v });
    } catch (error) {}
  }

  // 跳转指标设置
  gotoTarget() {
    this.$router.push('/home/<USER>/targetResult');
  }

  // 跳转至导出原卷
  gotoPaperExport() {
    this.$router.push('/home/<USER>');
  }

  backToList() {
    let pageType = this.$sessionSave.get('lookReportPage');
    if (pageType == 'deal') {
      this.$router.push({
        path: '/scan/deal',
        query: {
          userId: this.$sessionSave.get('loginInfo').id,
          schoolId: this.$sessionSave.get('schoolInfo').id,
        },
      });
      return;
    }

    let fromPage = this.$sessionSave.get('lookReportFrom');
    if (fromPage) {
      this.$router.push(fromPage);
      return;
    }
    this.$router.push({
      path: '/home/<USER>',
      query: { fromPage: 'examReportDetail' },
    });
  }

  setExamReport(item) {
    this.$sessionSave.set('innerClassList', null);
    this.$sessionSave.set('innerSubjectList', null);
    this.$sessionSave.set('loadComment', true);
    this.$sessionSave.set('reportDetail', item);
    this.$sessionSave.remove('subjectId');
    this.$sessionSave.set('downLoadState', item.statState);
    this.$router.push({ path: '/home/<USER>' });
    this.isMenuInit = false;
    this.key++;
    this.$nextTick(() => {
      this.init();
    });
  }
}
</script>

<style lang="scss" scoped>
.reportDetail_top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}
.downloadeBtn {
  margin-right: 15px;
}

.reportDetail {
  display: flex;
  flex-direction: column;
  font-family: Microsoft YaHei;
  height: auto !important;
  min-height: 290px;

  .reportDetail__back {
    font-size: 16px;
    font-weight: bold;
    color: #3f4a54;
    cursor: pointer;
    // margin-bottom : 20px;
  }
  .reportDetail__main {
    width: 100%;
    height: 100%;
    flex: 1;
    display: flex;
    align-items: flex-start;
    // overflow: hidden;
    // overflow-y: auto;
    .left-menu {
      position: -webkit-sticky;
      position: sticky;
      flex-shrink: 0;
      top: 0px;
      width: 166px;
      height: 242px;
      background: #fff;
      box-shadow: 0px 1px 3px 0px rgba(84, 84, 84, 0.1);
      border-radius: 6px;
      margin-right: 20px;
      li {
        cursor: pointer;
        width: 100%;
        height: 48px;
        line-height: 48px;
        font-size: 16px;
        color: #3f4a54;
        text-align: center;
        &.active {
          background-color: #f7fbff;
          color: #409eff;
          position: relative;
          &:before {
            content: '';
            position: absolute;
            width: 4px;
            height: 48px;
            background: #409eff;
            border-radius: 2px;
            left: 0;
            top: 0;
          }
        }
      }
    }
    .left-menu1 {
      position: -webkit-sticky;
      position: sticky;
      flex-shrink: 0;
      top: 0px;
      width: 166px;
      height: 195px;
      background: #fff;
      box-shadow: 0px 1px 3px 0px rgba(84, 84, 84, 0.1);
      border-radius: 6px;
      margin-right: 20px;
      li {
        cursor: pointer;
        width: 100%;
        height: 48px;
        line-height: 48px;
        font-size: 16px;
        color: #3f4a54;
        text-align: center;
        &.active {
          background-color: #f7fbff;
          color: #409eff;
          position: relative;
          &:before {
            content: '';
            position: absolute;
            width: 4px;
            height: 48px;
            background: #409eff;
            border-radius: 2px;
            left: 0;
            top: 0;
          }
        }
      }
    }
    .right-content {
      width: 0;
      flex: 1;
      display: flex;
      flex-direction: column;

      // height: 100%;
      height: auto;
      min-height: 270px;
      background: #fff;
      box-shadow: 0px 1px 3px 0px rgba(84, 84, 84, 0.1);
      border-radius: 6px;
      // padding: 20px 24px;
    }
  }
}

.reportDetail.desktop {
  padding: 10px 10px 0;
  height: 100% !important;

  .reportDetail__main {
    flex-direction: row-reverse;
    align-items: flex-end;

    .right-content {
      overflow-y: auto;
      height: 100%;

      ::v-deep {
        canvas {
          zoom: 0.95 !important;
          transform: scale(1.05);
          transform-origin: 0px 0px;
        }
      }
    }

    .left-sub-menu {
      margin-right: 0;
      margin-left: 20px;
    }
  }
}

.left-sub-menu {
  position: -webkit-sticky;
  position: sticky;
  flex-shrink: 0;
  top: 0px;
  width: 166px;
  background: #fff;
  box-shadow: 0px 1px 3px 0px rgba(84, 84, 84, 0.1);
  border-radius: 6px;
  margin-right: 20px;
  margin-bottom: 20px;

  ::v-deep {
    .el-menu-item {
      min-width: 166px !important;
      &.is-active {
        background-color: #e6f4fd;
        color: #409eff !important;
        position: relative;
        &:before {
          content: '';
          position: absolute;
          width: 4px;
          height: 50px;
          background: #409eff;
          border-radius: 2px;
          left: 0;
          top: 0;
        }
      }
    }
  }
}

.tip {
  color: red;
  font-size: 14px;
  font-weight: 400;
  margin-top: 5px;
  margin-left: 20px;
  line-height: 1.5;
}

.backtop {
  width: 60px;
  height: 60px;
}
</style>
