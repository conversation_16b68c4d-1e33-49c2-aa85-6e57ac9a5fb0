import * as httpApi from './index';
import { localSave, sessionSave } from '@/utils';

const BASE_API = process.env.VUE_APP_BASE_API;
const kklUrl = process.env.VUE_APP_KKLURL;
const MATH_CDN = process.env.VUE_APP_MATH_CDN;

// const mathContentReg1 = /<span[^>]*>\\\(([^<]*?)\\\)<\/span>/
// const mathContentReg2 = /\\\(([^<]*?)\\\)/
// const mathContentReg3 = /\$([^<]*?)\$/

const mathContentReg1 = /<span[^>]*>\\\(([\s\S]*?)\\\)<\/span>/g;
const mathContentReg2 = /\\\(([\s\S]*?)\\\)/g;
const mathContentReg3 = /\$([\s\S]*?)\$/g;
const mathContentReg4 = /<span[^>]*>\\\[([\s\S]*?)\\\]<\/span>/g;
const mathContentReg5 = /\\\[([\s\S]*?)\\\]/g;

/**
 *
 * @param rows
 * @returns {Promise<void>}
 */
export async function covertQuesList(rows) {
  for (let i = 0; i < rows.length; i++) {
    let it = rows[i];
    await convertQues(it);
  }
}

/**
 *
 * @param rows
 * @returns {Promise<void>}
 */
async function covertQuesList2(rows) {
  for (let i = 0; i < rows.length; i++) {
    let it = rows[i];
    await convertQues2(it);
  }
}

/**
 * 转换题目里的公式
 * @param it
 * @returns {Promise<void>}
 */
async function convertQues2(it) {
  it.topic = convertHtml(it.topic);
  it.content = convertHtml(it.content);
  it.answer = convertHtml(it.answer);
  it.analysis = convertHtml(it.analysis);
  if (it.optionText) {
    let list = [];
    for (let j = 0; j < it.optionText.length; j++) {
      list.push(convertHtml(it.optionText[j]));
    }
    it.optionText = list;
  }
}

/**
 * 转换题目里的公式
 * @param it
 * @returns {Promise<void>}
 */
async function convertQues(it) {
  it.data.qs.forEach(qs => {
    qs.ans.length > 0 &&
      qs.ans.forEach((ite, index) => {
        qs.ans[index] = convertHtml(ite, it.subject);
      });

    qs.content = convertHtml(qs.q_html, it.subject);
    qs.answer = convertHtml(qs.ans.join(','), it.subject);
    qs.analysis = convertHtml(qs.exp, it.subject);

    // 选择题需要单独处理拼接desc_html+opts_htmls
    if ([1, 8].includes(qs.type)) {
      let showType = qs.showType;
      if ([0, 1, 4].includes(showType)) showType = 1;

      let optionsHtml = '';
      let list = [];
      // 选项拆分数量
      for (let j = 0; j < qs.opts_htmls.length; j++) {
        let opts_htmls_str = qs.opts_htmls[j];
        let optStr = convertHtml(opts_htmls_str, it.subject);
        let letter = getUppercaseLetter(j + 1);

        switch (showType) {
          case 1:
            optionsHtml += `<div class="selectoption" style="margin:10px 0;"><span class="option-sort">${letter}. </span><label class="opt-label">${optStr}</label></div>`;
            break;

          case 2:
            optionsHtml += `<div class="selectoption dis-inline" style="margin:10px 5% 10px 0;min-width:45%;"><span class="option-sort">${letter}. </span><label class="opt-label">${optStr}</label></div>`
            break;

          case 3:
            optionsHtml += `<div class="selectoption dis-inline" style="margin:10px 2% 10px 0;min-width:23%;"><span class="option-sort">${letter}. </span><label class="opt-label">${optStr}</label></div>`
            break;
        }
        list.push(optStr);
      }
      if (optionsHtml) {
        if (showType === 3) optionsHtml = `<tr>${optionsHtml}</tr>`;

        qs.optionText = list;
        qs.topic = `<p>${convertHtml(
          qs.desc_html,
          it.subject
        )}</p><div class="choice-container clearfix">${optionsHtml}</div>`;
      } else {
        qs.topic = `<p>${convertHtml(qs.desc_html, it.subject)}</p>`;
      }
    } else {
      qs.topic = convertHtml(qs.q_html, it.subject);
    }
  });
  if (it.data.levelcode == '') {
    let ques = it.data.qs[0];
    it.topic = ques.topic;
    it.content = ques.content;
    it.answer = ques.answer;
    it.analysis = ques.analysis;
    it.optionText = ques.optionText;
  } else {
    let ans = [],
      option = [],
      exp = [];
    it.topic = convertHtml(it.data.desc_html, it.subject);
    it.content = convertHtml(it.data.q_html, it.subject);

    if (it.data.qs.length > 1) {
      it.data.qs.forEach(item => {
        ans.push(convertHtml(item.ans.join(''), it.subject));
        option.push(convertHtml(item.q_html, it.subject));
        item.exp == '' ? '' : exp.push(convertHtml(item.exp, it.subject));
      });
    } else {
      let qs = it.data.qs[0];
      ans.push(convertHtml(qs.ans.join(''), it.subject));
      qs.exp == '' ? '' : exp.push(convertHtml(qs.exp, it.subject));

      qs.opts_htmls.forEach(item => {
        option.push(convertHtml(item, it.subject));
      });
    }
    it.answer = ans;
    it.optionText = option;
    exp.length === 0 ? '' : (it.analysis = exp.join('\n'));
  }

  return it;
}

/**
 * 获取html里的公共转换成图片显示
 * @param html
 * @returns {Promise<string|*>}
 */
export function convertHtml(html, subject) {
  //是否为语文或英语学科
  const subjectIds = ['1', '3', '10', '12', '24', '25', 1, 3, 10, 12, 24, 25];
  let isChineseOrEnglish = subjectIds.includes(subject);
  if (!html) {
    return '';
  }
  if (isChineseOrEnglish) {
    return html;
  }

  html = html.replace(/<a[^>]*>(.*?)<\/a>/g, '$1');
  html = html.replace(/<span[^>]*class="MathJax_Preview"[^>]*><\/span>/g, '');
  // html = html.replace(/&amp;/g, "&");
  // html = html.replace(/&nbsp;/g, " ");
  // html = html.replace(/<br.?>/g, "");
  // html = html.replace(/&lt;/g, "<");
  // html = html.replace(/&gt;/g, ">");
  // let match
  // do {
  //     match = html.match(mathContentReg1)
  //     if (!match) {
  //         match = html.match(mathContentReg2)
  //     }
  //
  //     if (!match) {
  //         match = html.match(mathContentReg3)
  //     }
  //     if (!match) {
  //         break
  //     }
  //     let input = match[1]
  //
  //     input = input.replace(/&lt;/g, '\\lt ')
  //     input = input.replace(/&gt;/g, '\\gt ')
  //
  //     html = html.replace(match[0], `<img alt="${input}" class="math-tex-img" src="${MATH_CDN}/math/mathImage/${encodeURIComponent(encodeURIComponent(input))}.png"/>`)
  //
  // } while (match)
  html = html.replace(mathContentReg1, convertLatex2image);
  html = html.replace(mathContentReg2, convertLatex2image);
  html = html.replace(mathContentReg3, convertLatex2image);
  html = html.replace(mathContentReg4, convertLatex2image);
  html = html.replace(mathContentReg5, convertLatex2image);
  return html;
}
function convertLatex2image(all, input) {
  input = input.replace(/&amp;/g, '&');
  input = input.replace(/&nbsp;/g, ' ');
  input = input.replace(/<br.?>/g, '');
  input = input.replace(/&lt;/g, '<');
  input = input.replace(/&gt;/g, '>');
  input = input.replace(//g, '');
  try {
    const res = MathJax.tex2svg(input, { display: false });

    return `<span alt="${input}" class="math-tex-svg">${res.outerHTML}</span>`;
  } catch (e) {
    try {
      let html = katex.renderToString(input, {
        displayMode: true,
        leqno: false,
        fleqn: false,
        throwOnError: true,
        errorColor: '#cc0000',
        strict: 'warn',
        output: 'htmlAndMathml',
        trust: false,
        macros: { '\\f': 'f(#1)' },
      });
      return `<span alt="${input}" class="math-tex-svg">${html}</span>`;
    } catch (e) {
      console.error("convertLatex2image", e)
    }
  }

  console.log(input);
  return all;
  // return `<img alt="${input}" class="math-tex-img" src="${MATH_CDN}/mathsvg/${encodeURIComponent(encodeURIComponent(input))}.svg"/>`
}

/**
 * api转换
 * @type {{POST: API.POST, GET: API.GET}}
 */
const API = {
  GET: function (url, params) {
    return httpApi.GET(url, params, kklUrl);
  },
  POST: function (url, params) {
    return httpApi.POST(url, params, kklUrl);
  },
};

/**
 * api转换
 * @type {{POST: API.POST, GET: API.GET}}
 */
const BaseAPI = {
  GET: function (url, params) {
    return httpApi.GET(url, params, BASE_API);
  },
  POST: function (url, params) {
    return httpApi.POST(url, params, BASE_API);
  },
};

// 获取年级
// export const selectAllGrade = (params) => {
//     return API.GET('/pbook/grade/selectAll', params)
// }

// 获取年级
export const selectAllGrade = params => {
  return API.GET('/pbook/grade/selectExamGrade', params);
};
// 获取学科
export const findSubjectByPhase = params => {
  return API.GET('/pbook/subject/findAllSubByPhase', params);
};
// 获取类别
export const selectAllType = params => {
  return API.GET('/pbook/personalType/selectAll', params);
};
// 获取题面详情
export const chooseQuesSearch = params => {
  return new Promise(resolve => {
    BaseAPI.POST('/testbank/TBQuestion/getQuestionInfoBatch', params).then(async res => {
      await covertQuesList(res.data);
      resolve(res);
    });
  });
};

// 查询个册题库
export function findPersonBookQuesBank(data) {
  let subject = sessionSave.get('currentSubject');
  data.subjectId = data.subjectId || (subject && subject.id) || '11';
  return new Promise((resolve, reject) => {
    // API.POST("/ptask/search/findPersonBookQuesBank", data)
    API.POST('/pbook/search/getQuesWithPoint', data)
      .then(async res => {
        await covertQuesList(res.data.rows);
        resolve(res.data);
      })
      .catch(err => {
        reject(null);
      });
  });
}

// 查询个册加工进度
export const findPersonalTaskProgress = params => {
  return API.POST('/pbook/personality/findPersonalTaskProgress', params);
};
// 发送加工
export const saveBaseRelation = params => {
  return API.GET('pbook/_/saveBaseRelation', params);
};

export function getUppercaseLetter(number) {
  if (number < 1 || number > 26) {
    return '数字范围应为 1 到 26';
  }

  // 将数字转换为大写字母的 ASCII 码
  const uppercaseCharCode = number + 64;

  // 使用 String.fromCharCode() 方法将 ASCII 码转换为对应的字符
  const uppercaseLetter = String.fromCharCode(uppercaseCharCode);

  return uppercaseLetter;
}
/**
 * @description: jy以题推题
 * @param {*} params
 * @return {*}
 */
export const getQuesSearchConvert = params => {
  return new Promise((resolve, reject) => {
    // BaseAPI.POST('/questionbank/mockApp/getQuesSearchConvert', params).then(async res => {
    BaseAPI.POST('/questionbank/mockApp/getQuesSearchSame', params).then(async res => {
      await covertQuesList(res.data.rows);
      resolve(res);
    }).catch(reject);
  });
};

/**
 * @description: jy以题推题 - 题目详情
 * @param {*} params
 * @return {*}
 */
export const syncJYEooQueInfo = params => {
  return new Promise(resolve => {
    BaseAPI.POST('/questionbank/mockApp/syncJYEooQueInfo', params).then(async res => {
      await covertQuesList([res.data]);
      resolve(res);
    });
  });
};

/**
 * @description: 查询我的题库
 * @param {*} params
 * @return {*}
 */
export const getSchoolTbQuestionList = params => {
  return API.POST('/pbook/tbtestbank/getSchoolTbQuestionList', params);
};

/**
 * @description: 查询教材
 * @param {*} params
 * @return {*}
 */
export const getSchChapterAPI = params => {
  return API.POST('pbook/teachingProgress/schChapter', params);
};