<!--
 * @Description: 
 * @Author: 小圆
 * @Date: 2025-03-17 16:42:54
 * @LastEditors: 小圆
-->
<template>
  <div class="reference-sample report-wrapper" v-loading="loading">
    <headerFilter @updateFilter="updateFilter" ref="headerFilter" class="header-filter"></headerFilter>

    <div>
      <div class="titleLine">写作思路</div>
      <div class="writing-content">
        <p>
          {{ exampleComposition?.describe }}
        </p>
      </div>
    </div>
    <div>
      <div class="titleLine">
        参考范文
        <div class="operation-buttons" v-if="!isEditing">
          <el-button type="primary" size="mini" icon="el-icon-edit" @click="startEditing">编辑</el-button>
        </div>
        <div class="operation-buttons" v-else>
          <el-button type="success" size="mini" icon="el-icon-check" @click="saveContent">完成</el-button>
          <el-button size="mini" icon="el-icon-close" @click="cancelEditing">取消</el-button>
        </div>
      </div>
      <div class="writing-content">
        <vue-markdown v-if="!isEditing" :source="exampleComposition?.example"></vue-markdown>
        <tinymce-editor-vue v-else v-model="editingContent" @input="contentChange"></tinymce-editor-vue>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { getExampleCompositionAPI, saveExampleAPI } from '@/service/api';
import { Component, Vue } from 'vue-property-decorator';
import NoData from '@/components/noData.vue';
import HeaderFilter from '@/components/headerFilter.vue';
import VueMarkdown from 'vue-markdown';
import TinymceEditorVue from '@/pages/help/components/TinymceEditor.vue';

export interface ExampleComposition {
  thought: string;
  describe: string;
  example: string;
}

@Component({
  components: {
    NoData,
    HeaderFilter,
    VueMarkdown,
    TinymceEditorVue,
  },
})
export default class ReferenceSample extends Vue {
  // 当前选中的筛选项数据
  filterData = {
    classId: '',
    classIds: [],
    subjectId: '',
    phaseId: '',
    xfId: '',
    classList: [],
    aliasName: '', // 组合学科
    qType: 0, // 0:得分 1：赋分
    source: 0, // 0:成绩榜 1：组合成绩榜
    abPaper: '', // -1:普通 0:A卷 1:B卷
    quesInfo: null, // 作文题
  };

  // 作文范文
  exampleComposition: ExampleComposition | null = {
    describe: '',
    example: '',
    thought: '',
  };
  // 是否加载中
  loading = false;
  // 是否处于编辑状态
  isEditing = false;
  // 编辑时的临时内容
  editingContent = '';

  // 当前workId
  get workId() {
    let subjectId = this.filterData.subjectId;
    let subjectList = this.$sessionSave.get('innerNoRoleSubjectList');
    let subject = subjectList.find(item => item.id == subjectId);
    if (subject) {
      return subject.workIds[Number(this.filterData.abPaper)];
    }
    return '';
  }

  mounted() {}

  // 更新筛选条件
  updateFilter(filterData) {
    this.filterData = this.$deepClone(filterData);
    this.getExampleComposition();
  }

  // 获取作文范文
  async getExampleComposition() {
    this.loading = true;
    const res = await getExampleCompositionAPI({
      schoolId: this.$sessionSave.get('schoolInfo').id,
      workId: this.workId,
      quesNo: this.filterData.quesInfo?.quesNo,
      tQuesNo: this.filterData.quesInfo?.tQuesNo,
    });

    if (res.code == 1) {
      this.exampleComposition = res.data;
      // 将换行符转换为br标签，用于tinymce解析
      this.exampleComposition.example = this.exampleComposition.example.replace(/\n/g, '<br>');
    }
    this.loading = false;
  }

  // 开始编辑
  startEditing() {
    this.isEditing = true;
    this.editingContent = this.exampleComposition?.example || '';
  }

  // 保存内容
  async saveContent() {
    const example = this.editingContent;
    if (!example) {
      this.$message.warning('请输入范文');
      return;
    }
    this.isEditing = false;
    await saveExampleAPI({
      workId: this.workId,
      quesNo: this.filterData.quesInfo?.quesNo,
      tQuesNo: this.filterData.quesInfo?.tQuesNo,
      describe: this.exampleComposition.describe,
      example: example,
    });
    this.exampleComposition.example = example;
    this.$message.success('保存成功');
  }

  // 取消编辑
  cancelEditing() {
    this.isEditing = false;
    this.editingContent = '';
  }

  contentChange(content) {
    this.editingContent = content;
  }
}
</script>

<style scoped lang="scss">
@import './index.scss';

.reference-sample {
}

.titleLine {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.operation-buttons {
  display: inline-block;
  margin-left: auto;
}

.writing-content {
  padding: 10px;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  line-height: 1.8;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);
  }
}
</style>
