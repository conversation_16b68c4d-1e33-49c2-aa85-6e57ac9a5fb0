<!--
 * @Description: 填空题设置组件
 * @Author: liuyue <EMAIL>
 * @Date: 2025-07-23 13:59:25
 * @LastEditors: liuyue <EMAIL>
 * @LastEditTime: 2025-09-12 08:50:58
-->

<template>
  <div class="fill-setting-container">
    <!-- 第一步：制作识别模版 -->
    <div class="step-container">
      <div>
        <span>选择题号</span>
        <template v-for="item in quesDataWithTips">
          <el-button v-if="!item.hasTip" class="btn-item ellipsis-btn" :key="item.id" @click="onQuestionChange(item.id)"
            :type="item.id === currentQuestion.id ? 'primary' : 'default'">{{ item.name
            }}</el-button>
          <el-badge v-else is-dot class="btn-item" :key="'dot_' + item.id"><el-button class="ellipsis-btn"
              :key="item.id" @click="onQuestionChange(item.id)"
              :type="item.id === currentQuestion.id ? 'primary' : 'default'">{{
                item.name
              }}</el-button></el-badge>
        </template>
      </div>
      <div class="step-title">第1步：制作识别模板 （如作答区域有偏差，请手动调整或重新框选）</div>
      <div class="template-container" :style="{ pointerEvents: isReadonly ? 'none' : 'auto' }">
        <!-- 试卷图片 -->
        <div v-for="(img, index) in quesImgList" :key="`img-${index}`" :ref="`templateContainer-${img.page}`"
          :style="imageBoxStyle(img.pos)" class="exam-image-container">
          <img :src="img.url" class="exam-image" :style="imageStyle(img.pos)" @mousedown.stop="onMouseDown($event, img)"
            @mousemove="onMouseMove" @mouseup="onMouseUp" />
          <!-- 已框选区域 -->
          <template v-for="ques in smallList">
            <template v-for="(blank, bindex) in ques.lineList">
              <template v-for="(area, aindex) in blank.points">
                <template v-if="area.page == img.page">
                  <div class="selected-area" :class="{ 'active': area.active }"
                    :key="`area-${index}-${area.pos[0]}-${area.pos[1]}-${area.pos[2]}-${area.pos[3]}`" :style="{
                      position: 'absolute',
                      left: (area.pos[0] - img.pos[0] + imgMargin) * imgDpi + 'px',
                      top: (area.pos[1] - img.pos[1] + imgMargin) * imgDpi + 'px',
                      width: area.pos[2] * imgDpi + 'px',
                      height: area.pos[3] * imgDpi + 'px',
                      border: '2px solid #409EFF',
                      backgroundColor: 'rgba(64, 158, 255, 0.1)',
                      pointerEvents: isDrawing ? 'none' : 'auto'
                    }" @mousedown.stop="startDrag($event, area)">
                    <div class="area-label">{{ ques.quesNos }}-空{{ bindex + 1 }}</div>
                    <template v-if="!isReadonly">
                      <div class="delete-btn" @click.stop="deleteArea(ques, bindex)">
                        <i class="el-icon-delete"></i>
                      </div>
                      <div class="resize-handle tl" @mousedown.stop.prevent="startResize($event, area, 'tl')"></div>
                      <div class="resize-handle tm" @mousedown.stop.prevent="startResize($event, area, 'tm')"></div>
                      <div class="resize-handle tr" @mousedown.stop.prevent="startResize($event, area, 'tr')"></div>
                      <div class="resize-handle mr" @mousedown.stop.prevent="startResize($event, area, 'mr')"></div>
                      <div class="resize-handle br" @mousedown.stop.prevent="startResize($event, area, 'br')"></div>
                      <div class="resize-handle bm" @mousedown.stop.prevent="startResize($event, area, 'bm')"></div>
                      <div class="resize-handle bl" @mousedown.stop.prevent="startResize($event, area, 'bl')"></div>
                      <div class="resize-handle ml" @mousedown.stop.prevent="startResize($event, area, 'ml')"></div>
                    </template>
                  </div>
                </template>
              </template>
            </template>
          </template>

          <!-- 正在绘制的区域 -->
          <div v-if="isDrawing" class="drawing-area" :style="{
            left: `${Math.min(drawStartPos.x, drawCurrentPos.x)}px`,
            top: `${Math.min(drawStartPos.y, drawCurrentPos.y)}px`,
            width: `${Math.abs(drawCurrentPos.x - drawStartPos.x)}px`,
            height: `${Math.abs(drawCurrentPos.y - drawStartPos.y)}px`,
            position: 'absolute',
            border: '2px dashed #409EFF',
            backgroundColor: 'rgba(64, 158, 255, 0.1)',
            zIndex: 99
          }"></div>
        </div>
      </div>
      <div class="step-title">
        <span>第2步：设置答案 （如该空交由老师批改，可在该空的答案处输入$$）</span>
        <div v-if="!isReadonly" class="batch-score-setting">
        <span>批量设置每空</span>
        <el-input v-model="batchScore" class="batch-input" @input="checkBatchScore"
          @blur="applyBatchScore">
        </el-input>
        <span>分</span>
        <span style="margin-left: 20px;color:#409EFF;cursor: pointer;"
          @click="batchSetAnswerVisible = true">批量设置答案</span>
      </div>
      </div>

      <div class="answer-container">
        <table>
          <thead>
            <tr>
              <th>题号</th>
              <th>小题分</th>
              <th>小空分之和</th>
              <th>小空分设置</th>
              <th>小空答案（点击“+”添加备选答案）</th>
            </tr>
          </thead>
          <tbody>
            <template v-for="(item, qIndex) in smallList">
              <template v-for="(blank, index) in item.lineList">
                <tr :key="`b-${item.quesNo}-${index}`">
                  <td v-if="index == 0" :rowspan="item.lineList.length">{{ item.quesNos }}</td>
                  <td v-if="index == 0" :rowspan="item.lineList.length">{{ item.score }}</td>
                  <td v-if="index == 0" :rowspan="item.lineList.length">
                    <div>{{ fillSumScore(item) }}</div>
                    <div v-if="item.score != fillSumScore(item)" style="color: red;font-size: 12px;">※总分与小空分之和不等，请重新设置
                    </div>
                  </td>
                  <td><el-input v-model="blank.score" :disabled="isReadonly" @input="checkScore(blank)" class="score-input"></el-input></td>
                  <td class="td-answer">
                    <template v-for="(ans, aindex) in blank.answer">
                      <template v-if="isMathSubject">
                        <div style="position: relative;display: flex;">
                          <div style="display: inline-flex;align-items: center;">空{{ index + 1 }}</div>
                          <el-popover
                            placement="right"
                            trigger="hover">
                            <div v-html="getAnsHtml(ans)"></div>
                             <math-field slot="reference" :key="`a-${index}-${aindex}-${blank.answer.length}`"
                              ref="mathfield" virtual-keyboard-mode="off"
                              :style="{ width: (aindex == blank.answer.length - 1) ? '420px' : '420px', minHeight: '50px', border: '1px solid #ccc', padding: '8px 20px 8px 8px', fontSize: '18px' }"
                              @input="(e) => { updateAnswer(blank, aindex, e.target.getValue('latex')) }">{{ ans
                              }}</math-field>
                          </el-popover>
                          <div v-if="aindex == blank.answer.length - 1" class="add-btn"
                            @click="addFillOpts(blank)" title="增加备选答案">+</div>
                          <div v-if="blank.answer.length > 1" class="delete-btn"
                            @click.stop="deleteAnswer(blank, aindex)">
                            <i class="el-icon-delete"></i>
                          </div>
                        </div>
                      </template>
                      <template v-else>
                        <div style="position: relative;">空{{ index + 1 }}
                          <el-popover
                            placement="right"
                            trigger="hover"
                            :offset="10">
                            <div>{{ ans || '&nbsp;' }}</div>
                            <el-input slot="reference" v-model="blank.answer[aindex]" :key="`a-${index}-${aindex}-${blank.answer.length}`"
                              style="width: 400px;" class="answer-input">
                              <el-button v-if="aindex == blank.answer.length - 1" slot="append"
                                @click="addFillOpts(blank)" title="增加备选答案">+</el-button>
                            </el-input>
                          </el-popover>
                          <div v-if="blank.answer.length > 1" class="delete-btn"
                            @click.stop="deleteAnswer(blank, aindex)">
                            <i class="el-icon-delete"></i>
                          </div>
                        </div>
                      </template>
                    </template>
                  </td>
                </tr>
              </template>
            </template>
          </tbody>
        </table>
      </div>

      <div class="step-actions">
        <div class="buttons">
          <el-button type="primary" @click="saveSettings">保存设置</el-button>
        </div>
      </div>
    </div>
    <el-dialog title="新增小空" :visible.sync="addFillLineVisible" width="30%" :before-close="closeDialog">
      <div>
        增加第
        <el-select v-model="quesId" placeholder="">
          <el-option v-for="item in smallList" :key="item.id" :label="item.quesNos" :value="item.id"></el-option>
        </el-select>
        小题，
        第<el-input-number v-model="fillIndex" size="mini" :min="1" :max="100" placeholder=""></el-input-number>空
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" :disabled="isReadonly" @click="addFillLine">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog title="批量设置答案" width="40%" :visible.sync="batchSetAnswerVisible" :before-close="closeDialog">
      <div>
        <span>过滤序号：</span>
        <el-switch v-model="filterNumber" active-text="是" inactive-text="否"></el-switch>
      </div>
      <div style="color:#606266;margin:5px 0;">请粘贴或输入答案，每行一个，备选答案以'/'进行间隔</div>
      <!-- 带行号的 textarea -->
      <div class="textarea-with-lines">
        <!-- 行号 -->
        <div class="line-numbers">
          <div v-for="n in lineCount" :key="n">{{ n }}</div>
        </div>
        <!-- 输入框 -->
        <textarea v-model="answerText" class="custom-textarea" :rows="10"
          @input="() => { this.lineCount = this.answerText.split(/\r?\n/).length || 1 }"
          placeholder="请粘贴或输入答案，每行一个，备选答案以'/'进行间隔"></textarea>
      </div>

      <!-- 底部按钮 -->
      <span slot="footer" class="dialog-footer">
        <el-button @click="batchSetAnswerVisible = false">取消</el-button>
        <el-button type="primary" @click="batchSetAnswer">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { IQUES_SCAN_MODE } from '@/typings/card';
import "mathlive/mathlive-static.css";
import "mathlive/mathlive.js";
import { distributeScore } from '@/utils/number.js';
import { isSupportMathSubject } from '@/utils';
export default {
  name: 'FillSetting',
  props: {
    aiFillQuesData: {
      type: Array,
      default: () => []
    },
    isReadonly: {
      type: Boolean,
      default: false
    },
    paperNo: {
      type: String,
      default: ''
    },
    mainPaperNo: {
      type: String,
      default: ''
    },
    subjectId: {
      type: String,
      default: ''
    },
    imgList: {
      type: Array,
      default: () => []
    },

  },
  data() {
    return {
      quesData: [],
      smallList: [],
      ponitsList: [],
      currentQuestion: {},
      activeQuesLine: null,
      isDrawing: false, // 是否正在绘制
      drawStartPos: { x: 0, y: 0 }, // 绘制起点
      drawCurrentPos: { x: 0, y: 0 }, // 绘制当前点
      isResizing: false, // 是否正在调整大小
      resizingIndex: -1, // 正在调整大小的区域索引
      resizingHandle: '', // 正在拖动的手柄类型
      resizeStartPos: { x: 0, y: 0 }, // 调整大小起点
      dragStartPos: { x: 0, y: 0 }, // 拖动起点
      batchScore: '', // 批量设置分值
      //图片分辨率
      imgDpi: 3.344,
      quesImgList: [],
      imgMargin: 5,
      tempArea: { page: 0, pos: [] },
      addFillLineVisible: false,
      batchSetAnswerVisible: false,
      quesId: "",
      fillIndex: 1,
      answerText: "",//批量设置答案内容
      filterNumber: false, // 控制是否过滤序号
      lineCount: 1,
    };
  },
  computed: {
    imageBoxStyle() {
      return (pos) => {
        return {
          width: `${(Number(pos[2]) + this.imgMargin * 2) * this.imgDpi}px`,
          height: `${(Number(pos[3]) + this.imgMargin * 2) * this.imgDpi}px`
        };
      }
    },
    imageStyle() {
      return (pos) => {
        return {
          top: `-${(pos[1] - this.imgMargin) * this.imgDpi}px`,
          left: `-${Math.max(pos[0] - this.imgMargin, 0) * this.imgDpi}px`
        }
      }
    },
    fillSumScore() {
      return (ques) => {
        return ques.lineList.reduce((sum, item) => {
          return (Math.round(sum * 100 + Number(item.score) * 100) / 100)
        }, 0);
      }
    },
    isMathSubject() {
      return isSupportMathSubject(this.subjectId);
    },
    quesDataWithTips() {
      return this.quesData.map(ques => {
        let hasTip = false;

        if (ques.data && ques.data.length > 0) {
          ques.data.forEach(qitem => {
            const items = qitem.data?.length > 0 ? qitem.data : [qitem];
            items.forEach(item => {
              if (item.scanMode == IQUES_SCAN_MODE.AI_FILL && item.lineList) {
                let blankScore = 0;
                item.lineList.forEach(blank => {
                  if (typeof blank.answer == "string") {
                    blank.answer = [blank.answer];
                  }
                  if (blank.answer && blank.answer.join('') == "") {
                    hasTip = true;
                  }
                  blankScore = blankScore + Number(blank.score) * 100;
                });
                if (blankScore / 100 != item.score) {
                  hasTip = true;
                }
              }
            });
          });
        }

        return {
          ...ques,
          hasTip
        };
      });
    }
  },
  async created() {
    this.quesData = JSON.parse(JSON.stringify(this.aiFillQuesData));
    if (this.quesData && this.quesData.length > 0) {
      this.onQuestionChange(this.quesData[0].id);
    }
  },
  mounted() {
  },
  methods: {

    // 题目变更
    onQuestionChange(quesId) {
      this.smallList = [];
      this.ponitsList = [];
      this.currentQuestion = this.quesData.find(item => item.id === quesId) || {};
      this.currentQuestion.data.forEach(qitem => {
        const items = qitem.data?.length > 0 ? qitem.data : [qitem];

        items.forEach(item => {
          if (item.scanMode == IQUES_SCAN_MODE.AI_FILL) {
            if (!item.lineList?.length) {
              this.$set(item, 'lineList', []);
              const avgScoreList = distributeScore(item.score, item.points.length);
              item.points.forEach((point,index) => {
                item.lineList.push({
                  answer: '',
                  score: avgScoreList[index],
                });
              });
            }
            for (let index = item.lineList.length - 1; index >= 0; index--) {
              let blank = item.lineList[index];
              // 处理答案格式
              if (blank.answer) {
                if (typeof blank.answer == "string") {
                  blank.answer = [blank.answer];
                }
              } else {
                this.$set(blank, 'answer', [""]);
              }
              let points;
              if (Array.isArray(blank.points)) {
                points = blank.points;
              } else {
                // 处理坐标点
                if (!blank.points) {
                  try {
                    points = JSON.parse(JSON.stringify(item.points[index]));
                  } catch (e) {
                    item.lineList.splice(index, 1);
                    console.warn("存在无坐标小空，移除对应空", e)
                    continue;
                  }
                } else {
                  points = JSON.parse(JSON.stringify(blank.points));
                }
                if (points.pos.length > 4) {
                  const result = [];
                  for (let i = 0; i < points.pos.length; i += 4) {
                    result.push(points.pos.slice(i, i + 4));
                  }
                  points = result.map(pos => ({
                    page: points.page,
                    pos: pos
                  }));
                } else {
                  points = [points];
                }
              }
              this.$set(blank, 'points', points);
            }

            this.smallList.push(item);
            this.ponitsList.push(...item.points);
          }
        });
      });
      this.quesId = this.smallList[0].id;
      this.getQuesAreaAndPage();
    },
    // 获取题目区域和页码
    getQuesAreaAndPage() {
      this.quesImgList = [];
      let pages = [];
      this.ponitsList.forEach(item => {
        if (pages[item.page]) {
          pages[item.page].push(item.pos);
        } else {
          pages[item.page] = [item.pos];
        }
      });
      pages.forEach((page, index) => {
        if (!page) return;
        // 初始化边界值
        let minX = Infinity, minY = Infinity;
        let maxX = -Infinity, maxY = -Infinity;

        for (const [x, y, width, height] of page) {
          minX = Math.min(minX, Number(x));
          minY = Math.min(minY, Number(y));
          maxX = Math.max(maxX, Number(x) + Number(width));
          maxY = Math.max(maxY, Number(y) + Number(height));
        }
        this.quesImgList.push({ page: index, pos: [minX, minY, maxX - minX, maxY - minY], url: this.imgList[index] })
      })
    },
    // 通用的分数格式化方法
    formatScore(score) {
      let formattedScore = String(score).replace(/[^\d.]/g, '').replace(/\.{2,}/g, '');
      let parts = formattedScore.split('.');
      if (parts.length > 1) {
        parts = parts.splice(0, 2);
        // 如果有小数点，仅保留一位小数
        parts[1] = parts[1].slice(0, 1);
      }
      formattedScore = parts.join('.');
      if (Number(formattedScore) > 100) {
        formattedScore = '100';
      }
      return formattedScore;
    },
    // 检查题目分数
    checkScore(row) {
      this.$set(row, 'score', this.formatScore(row.score));
    },
    // 检查批量设置分值的输入
    checkBatchScore() {
      this.batchScore = this.formatScore(this.batchScore);
    },
    // 批量应用分值到所有题目的lineList
    applyBatchScore() {
      if (!this.batchScore) {
        this.$message({
          message: '请输入要设置的分值',
          type: 'warning',
        });
        return;
      }

      let updatedCount = 0;
      this.smallList.forEach(ques => {
        if (ques.lineList?.length) {
          ques.lineList.forEach(fill => {
            fill.score = this.batchScore;
            updatedCount++;
          });
        }
      });

      if (updatedCount > 0) {
        this.$message({
          message: `已成功设置 ${updatedCount} 个小空的分值为 ${this.batchScore}`,
          type: 'success',
        });
      } else {
        this.$message({
          message: '没有找到可设置的小空',
          type: 'warning',
        });
      }
    },
    getAnsHtml(ans) {
      return MathJax.tex2svg(MathjaxHelper.toMathJax(ans)).outerHTML;
    },
    showMathKeyboard(ques, index, blank) {
      let detailAnswer = blank.answer.map(item => {
        return { blankAnswer: item, containFormula: false }
      });
      let param = {
        isMQ: true,
        css: { width: '895px' },
        data: {
          title: `${ques.quesNos}-空${index + 1}`,
          answer: {
            blankScore: blank.score,
            detailAnswer: detailAnswer
          },
        },
      };
    },
    // 添加小空选项
    addFillOpts(blank) {
      let hasNull = blank.answer.some(item => item == "");
      if (hasNull) {
        this.$message({
          message: '请先填写小空答案',
          type: 'warning',
        });
        return;
      }
      blank.answer.push("");
    },
    // 删除小空选项
    deleteAnswer(blank, aindex) {
      blank.answer.splice(aindex, 1);
    },
    updateAnswer(blank, aindex, ans) {
      this.$set(blank.answer, aindex, ans)
    },
    // 鼠标按下事件
    onMouseDown(e, area) {
      // 阻止默认事件，防止触发浏览器的拖动
      e.preventDefault();

      // 如果点击的是区域或区域内的元素，不处理
      if (e.target.closest('.selected-area') ||
        e.target.classList.contains('resize-handle') ||
        e.target.classList.contains('delete-btn')) {
        return;
      }

      // 获取相对于容器的坐标
      const container = this.$refs[`templateContainer-${area.page}`];
      const rect = container[0].getBoundingClientRect();
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;

      this.isDrawing = true;
      this.drawStartPos = { x, y };
      this.drawCurrentPos = { x, y };
      this.tempArea = { page: area.page, pos: [x, y, 0, 0] }
      console.log(this.tempArea)
    },

    // 鼠标移动事件
    onMouseMove(e) {
      // 阻止默认事件，防止触发浏览器的拖动
      e.preventDefault();

      if (!this.isDrawing) return;

      const container = this.$refs[`templateContainer-${this.tempArea.page}`];
      const rect = container[0].getBoundingClientRect();
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;

      this.drawCurrentPos = { x, y };
    },

    // 鼠标释放事件
    onMouseUp(e) {
      // 阻止默认事件，防止触发浏览器的拖动
      e.preventDefault();

      if (!this.isDrawing) return;

      const container = this.$refs[`templateContainer-${this.tempArea.page}`];
      const rect = container[0].getBoundingClientRect();
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;

      this.drawCurrentPos = { x, y };

      // 计算区域
      const minX = Math.min(this.drawStartPos.x, this.drawCurrentPos.x);
      const minY = Math.min(this.drawStartPos.y, this.drawCurrentPos.y);
      const width = Math.abs(this.drawCurrentPos.x - this.drawStartPos.x);
      const height = Math.abs(this.drawCurrentPos.y - this.drawStartPos.y);

      // 添加新区域（最小尺寸检查）
      if (width > 20 && height > 20) {
        this.tempArea.pos = [minX / this.imgDpi, minY / this.imgDpi, width / this.imgDpi, height / this.imgDpi]
        this.addFillLineVisible = true;
      }

      // 重置绘制状态
      this.isDrawing = false;
    },

    // 开始调整大小
    startResize(e, area, handle) {
      e.preventDefault();
      e.stopPropagation();

      this.isResizing = true;
      this.resizingHandle = handle;
      this.resizeStartPos = {
        x: e.clientX,
        y: e.clientY
      };

      // 激活当前区域
      this.activateArea(area);

      // 添加全局事件监听
      document.addEventListener('mousemove', this.onResizeMove);
      document.addEventListener('mouseup', this.onResizeEnd);
    },

    // 调整大小移动
    onResizeMove(e) {
      if (!this.isResizing) return;

      e.preventDefault();

      const area = this.activeQuesLine;
      const deltaX = (e.clientX - Number(this.resizeStartPos.x)) / this.imgDpi;
      const deltaY = (e.clientY - Number(this.resizeStartPos.y)) / this.imgDpi;

      const newRect = { x: Number(area.pos[0]), y: Number(area.pos[1]), width: Number(area.pos[2]), height: Number(area.pos[3]) };

      // 根据不同的手柄类型调整区域
      switch (this.resizingHandle) {
        case 'tl': // 左上
          newRect.x += deltaX;
          newRect.y += deltaY;
          newRect.width -= deltaX;
          newRect.height -= deltaY;
          break;
        case 'tm': // 上中
          newRect.y += deltaY;
          newRect.height -= deltaY;
          break;
        case 'tr': // 右上
          newRect.y += deltaY;
          newRect.width += deltaX;
          newRect.height -= deltaY;
          break;
        case 'mr': // 右中
          newRect.width += deltaX;
          break;
        case 'br': // 右下
          newRect.width += deltaX;
          newRect.height += deltaY;
          break;
        case 'bm': // 下中
          newRect.height += deltaY;
          break;
        case 'bl': // 左下
          newRect.x += deltaX;
          newRect.width -= deltaX;
          newRect.height += deltaY;
          break;
        case 'ml': // 左中
          newRect.x += deltaX;
          newRect.width -= deltaX;
          break;
      }

      // 确保尺寸不小于最小值
      if (newRect.width < 5) {
        if (['tl', 'bl', 'ml'].includes(this.resizingHandle)) {
          newRect.x = area.pos[0] + area.pos[2] - 5;
        }
        newRect.width = 5;
      }

      if (newRect.height < 5) {
        if (['tl', 'tm', 'tr'].includes(this.resizingHandle)) {
          newRect.y = area.pos[1] + area.pos[3] - 5;
        }
        newRect.height = 5;
      }

      // 更新区域 - 创建新数组以确保视图更新
      area.pos = [newRect.x, newRect.y, newRect.width, newRect.height]

      // 更新起始位置
      this.resizeStartPos = {
        x: e.clientX,
        y: e.clientY
      };
    },

    // 结束调整大小
    onResizeEnd(e) {
      if (!this.isResizing) return;

      e.preventDefault();
      this.isResizing = false;

      // 移除全局事件监听
      document.removeEventListener('mousemove', this.onResizeMove);
      document.removeEventListener('mouseup', this.onResizeEnd);
    },

    // 激活区域
    activateArea(area) {
      if (this.activeQuesLine) {
        this.$set(this.activeQuesLine, 'active', false);
      }
      // 设置激活状态
      this.activeQuesLine = area;
      this.$set(area, 'active', true);

      // 添加鼠标移动事件监听，用于拖动整个区域
      if (!this.isResizing) {
        document.addEventListener('mousemove', this.onAreaDragMove);
        document.addEventListener('mouseup', this.onAreaDragEnd);

        // 记录当前鼠标位置作为拖动起点
        this.dragStartPos = {
          mouseX: window.event ? window.event.clientX : 0,
          mouseY: window.event ? window.event.clientY : 0,
          areaX: area.pos[0],
          areaY: area.pos[1]
        };
      }
    },

    // 开始拖动整个区域
    startDrag(e, area) {
      if (this.isReadonly) {
        return;
      }
      e.preventDefault();
      e.stopPropagation();

      // 检查是否点击在调整手柄上
      if (e.target.classList.contains('resize-handle') || e.target.classList.contains('delete-btn')) {
        return;
      }
      // 激活当前区域
      this.activateArea(area);

      // 记录鼠标初始位置和区域初始位置
      this.dragStartPos = {
        mouseX: e.clientX,
        mouseY: e.clientY,
        areaX: area.pos[0],
        areaY: area.pos[1]
      };

      // 添加全局事件监听
      document.addEventListener('mousemove', this.onAreaDragMove);
      document.addEventListener('mouseup', this.onAreaDragEnd);
    },

    // 区域拖动移动
    onAreaDragMove(e) {
      if (this.isResizing) return;
      e.preventDefault();

      // 计算鼠标移动的距离，需要考虑DPI缩放
      const deltaX = (e.clientX - Number(this.dragStartPos.mouseX)) / this.imgDpi;
      const deltaY = (e.clientY - Number(this.dragStartPos.mouseY)) / this.imgDpi;

      // 更新区域位置
      this.activeQuesLine.pos = [Number(this.dragStartPos.areaX) + deltaX, Number(this.dragStartPos.areaY) + deltaY, this.activeQuesLine.pos[2], this.activeQuesLine.pos[3]]
    },

    // 区域拖动结束
    onAreaDragEnd(e) {
      e.preventDefault();
      document.removeEventListener('mousemove', this.onAreaDragMove);
      document.removeEventListener('mouseup', this.onAreaDragEnd);
    },

    // 删除区域
    deleteArea(ques, index) {
      ques.lineList.splice(index, 1)
    },
    addFillLine() {
      const ques = this.smallList.find(item => item.id === this.quesId);
      if (ques) {
        const imgPage = this.quesImgList.find(img => img.page == this.tempArea.page);
        let pos = [
          this.tempArea.pos[0] + imgPage.pos[0] - this.imgMargin,
          this.tempArea.pos[1] + imgPage.pos[1] - this.imgMargin,
          this.tempArea.pos[2],
          this.tempArea.pos[3]
        ]
        ques.lineList.splice(this.fillIndex - 1, 0, {
          answer: [''],
          score: 0,
          points: [{ page: this.tempArea.page, pos }]
        })
      }
      this.closeDialog();
    },
    batchSetAnswer() {
      // 按换行分隔，并去掉首尾空格和空行
      const arr = this.answerText
        .split(/\r?\n/)
        .map(item => {
          let line = item.trim();
          if (this.filterNumber) {
            // 匹配  "1. "  "1。"  "1、"  "① " 这种前缀
            line = line.replace(/^\s*\d+[\.。、．)]\s*/, "");
          }
          return line;
        })
        .filter(item => item !== "");

      this.smallList.forEach(item => {
        item.lineList.forEach(blank => {
          if(!arr.length)return
          const ans = arr.shift();
          blank.answer = ans.split('/');
        })
      })
      this.answerText = "";
      this.lineCount = 1;
      this.closeDialog();
    },
    closeDialog() {
      this.addFillLineVisible = false;
      this.batchSetAnswerVisible = false;
    },

    // 保存设置
    saveSettings() {
      this.aiFillQuesData.forEach((item, index) => {
        item.data.forEach((qitem, qindex) => {
          const items = qitem.data?.length > 0 ? qitem.data : [qitem];
          const _data = this.quesData[index].data[qindex];
          const nItems = _data.data?.length > 0 ? _data.data : [_data];
          items.forEach((sitem, sindex) => {
            if (sitem.scanMode == IQUES_SCAN_MODE.AI_FILL) {
              let ques = nItems[sindex];
              if (ques) {
                ques = JSON.parse(JSON.stringify(ques));
                ques.lineList.forEach((line, index) => {
                  if (!line.answer) {
                    line.answer = [];
                  }
                  if (!line.points) {
                    line.points = [];
                  }
                  if (line.answer.length > 1) {
                    line.answer = line.answer.filter(item => item.trim() != "");
                  }
                  line.answer = line.answer.map(ans => {
                    return MathjaxHelper.toMathJax(ans.trim());
                  })
                  if (line.answer.toString() != sitem.lineList[index]?.answer?.toString()) {
                    line.updatedAt = new Date().getTime()
                  }
                  let result;
                  if (Array.isArray(line.points)) {
                    result = { page: line.points[0].page, pos: [] };
                    if (line.points?.length > 1) {
                      line.points.forEach(item => {
                        result.pos.push(...item.pos)
                      })
                    } else {
                      result.pos = line.points[0].pos;
                    }
                  } else {
                    result = line.points;
                  }

                  line.points = result;
                  if (line.points?.pos?.toString() != sitem.lineList[index]?.points?.pos?.toString()) {
                    line.updatedAt = new Date().getTime()
                  }
                  delete line.active;
                })
                this.$set(sitem, 'lineList', ques.lineList);
              }
            }
          });
        })
      })
      // 发送保存事件
      this.$emit('saveAICorrectQue');
    },
  },
  beforeDestroy() {
    // 移除全局事件监听
    document.removeEventListener('mousemove', this.onResizeMove);
    document.removeEventListener('mouseup', this.onResizeEnd);
    document.removeEventListener('mousemove', this.onAreaDragMove);
    document.removeEventListener('mouseup', this.onAreaDragEnd);
  }
};
</script>

<style lang="scss" scoped>
.fill-setting-container {
  width: 100%;
  padding: 20px;
  background-color: #fff;

  .step-container {
    margin-bottom: 30px;

    >div:first-child {

      span {
        margin-right: 10px;
        font-weight: 600;
        color: #606266;
      }

      .btn-item {
        margin-right: 10px;
        margin-bottom: 10px;
        margin-left: 10px;
      }

      ::v-deep .el-badge {
        vertical-align: unset !important;
      }

      .ellipsis-btn {
        max-width: 200px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    .step-title {
      font-size: 16px;
      font-weight: bold;
      margin: 20px 0;
      color: #303133;
      background-color: #f2f6fc;
      padding: 10px 15px;
      border-left: 4px solid #409EFF;
      border-radius: 4px;
    }
  }

  .template-container {
    position: relative;
    height: fit-content;
    width: fit-content;
    border: 1px solid #dcdfe6;
    background: #f5f7fa;
    overflow: auto;
    margin: 15px 0;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12);

    .exam-image-container {
      position: relative;
      overflow: hidden;
      border-bottom: 1px solid #dcdfe6;

      .exam-image {
        height: 990px;
        width: auto;
        position: absolute;
      }

      .selected-area {
        cursor: move;
        box-shadow: 0 0 8px rgba(0, 0, 0, 0.2);

        .area-label {
          position: absolute;
          top: 0;
          left: 0;
          background: #409effc6;
          color: white;
          padding: 3px 8px;
          border-radius: 3px;
          font-size: 12px;
          z-index: 10;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
          white-space: nowrap;
        }

        .delete-btn {
          position: absolute;
          top: -20px;
          right: -20px;
          width: 24px;
          height: 24px;
          background: #f56c6c;
          color: white;
          border-radius: 50%;
          display: none;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          z-index: 10;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
          transition: all 0.2s;

          &:hover {
            transform: scale(1.1);
            background: #f78989;
          }
        }

        .resize-handle {
          display: none;
          position: absolute;
          width: 10px;
          height: 10px;
          background: white;
          border: 2px solid #409EFF;
          z-index: 15;
          border-radius: 50%;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);

          &.tl {
            top: -5px;
            left: -5px;
            cursor: nw-resize;
          }

          &.tm {
            top: -5px;
            left: 50%;
            margin-left: -5px;
            cursor: n-resize;
          }

          &.tr {
            top: -5px;
            right: -5px;
            cursor: ne-resize;
          }

          &.mr {
            top: 50%;
            right: -5px;
            margin-top: -5px;
            cursor: e-resize;
          }

          &.br {
            bottom: -5px;
            right: -5px;
            cursor: se-resize;
          }

          &.bm {
            bottom: -5px;
            left: 50%;
            margin-left: -5px;
            cursor: s-resize;
          }

          &.bl {
            bottom: -5px;
            left: -5px;
            cursor: sw-resize;
          }

          &.ml {
            top: 50%;
            left: -5px;
            margin-top: -5px;
            cursor: w-resize;
          }
        }

        &:not(.active):hover,
        &.active {
          border: 2px solid #ff7b00 !important;
          z-index: 99 !important;

          .area-label {
            background: #ff7b00c6 !important;
          }

          .resize-handle {
            display: block;
            border: 2px solid #ff7b00 !important;
          }

          .delete-btn {
            display: flex;
          }
        }

        &.active {
          z-index: 999 !important; // 只有active状态需要更高的z-index
        }
      }

      .drawing-area {
        pointer-events: none;
      }
    }
  }

  .area-active {
    border: 2px solid #409EFF !important;
  }

  .batch-score-setting {
    display: inline-block;
    align-items: center;
    background-color: #f2f6fc;
    padding: 12px 15px;
    border-radius: 4px;

    span {
      font-weight: 600;
      color: #606266;
    }

    .batch-input {
      width: 80px;
      margin: 0 10px;
    }
  }

  .answer-container {
    margin: 20px 0;

    table {
      border-collapse: collapse;
      width: 80%;
      border: 1px solid #dcdfe6;
      border-radius: 4px;
      overflow: hidden;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    }

    th {
      background-color: #f5f7fa;
      color: #606266;
      font-weight: 600;
      padding: 12px 8px;
      border: 1px solid #dcdfe6;
    }

    td {
      border: 1px solid #dcdfe6;
      text-align: center;
      padding: 10px 8px;
      color: #606266;
      width: 150px;

      &.td-answer {
        text-align: left;
        min-width: 480px;

        .add-btn {
          margin-left: -30px;
          display: inline-flex;
          justify-content: center;
          align-items: center;
          width: 30px;
          cursor: pointer;
          border: 1px solid rgb(204, 204, 204);
          background: #f5f7fa;
          border-radius: 4px;
          border-left: 0;
          border-top-left-radius: 0;
          border-bottom-left-radius: 0;
        }

        .delete-btn {
          position: absolute;
          text-align: center;
          width: 24px;
          height: 24px;
          background: #f56c6c;
          color: white;
          border-radius: 50%;
          cursor: pointer;
          top: -12px;
          right: 40px;
        }

        .fake-input-container {
          display: inline-flex;
          border: 1px solid #d1d5db;
          border-radius: 6px;
          overflow: hidden;
          margin-bottom: 2px;

          .fake-input {
            flex: 1;
            padding: 6px 10px;
            outline: none;
            cursor: text;
            min-height: 30px;
            width: 260px;
            overflow: hidden;

            ::v-deep .MathJax {
              margin: 0;
            }
          }

          .suffix {
            width: 35px;
            display: flex;
            align-items: center;
            padding: 6px 10px;
            color: #6b7280;
            border-left: 1px solid #e5e7eb;
            cursor: pointer;
            background: url('~@/assets/fx.png') no-repeat center center;
          }
        }
      }
    }

    .score-input {
      width: 100px;
    }

    .answer-input {
      width: 100%;
      margin-bottom: 2px;
    }
  }

  .step-actions {
    display: flex;
    align-items: center;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #ebeef5;

    .buttons {
      margin-left: auto;

      .el-button {
        padding: 12px 25px;
        font-size: 14px;
      }
    }
  }

  .textarea-with-lines {
    display: flex;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    font-family: monospace;

    .line-numbers {
      padding: 6px 8px;
      background: #f5f7fa;
      color: #909399;
      text-align: right;
      user-select: none;
      border-right: 1px solid #dcdfe6;

      div {
        line-height: 20px;
      }
    }

    .custom-textarea {
      flex: 1;
      border: none;
      outline: none;
      resize: none;
      padding: 6px;
      font-size: 14px;
      line-height: 20px;
      font-family: monospace;
    }
  }
}

.el-dialog {
  .el-select {
    width: 100px;
    margin: 0 10px;
  }

  .el-input-number {
    width: 100px;
    margin: 0 10px;
  }

  .dialog-footer {
    text-align: right;
  }
}
</style>
