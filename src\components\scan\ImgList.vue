<template>
  <div class="img-box" v-if="currentRow">
    <!-- 有扫描数据 -->
    <template v-if="currentRow.image && currentRow.image != ''">
      <picture>
        <source :srcset="getImage(currentRow)" type="image,jpeg" />
        <img class="img" :src="removeAvifParam(getImage(currentRow))" @load="loadImage" />
      </picture>
      <template v-if="isShowSubmitBtn">
        <el-button
          style="position: absolute; right: 30px; bottom: 5px"
          type="primary"
          size="small"
          @click="submitEditQues(currentRow, false)"
        >
          提交本页
        </el-button>
        <el-dropdown class="img-dropdown-menu">
          <span class="el-dropdown-link">
            <i slot="reference" class="el-icon-more"></i>
          </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item @click.native="setPointError(currentRow)"
              >设为定位异常
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <el-switch
          class="img-switch"
          v-model="showQuesScore"
          active-text="显示每题得分"
          inactive-text="隐藏每题得分"
        >
        </el-switch>
      </template>
    </template>
    <!-- 无扫描图片 -->
    <template v-else>
      <el-image class="img" :src="getOriginImage(currentRow)" @load="loadImage"> </el-image>
      <el-button
        style="position: absolute; right: 30px; font-size: 25px"
        type="text"
        @click="rotateImg('left')"
      >
        <i class="el-icon-refresh-left"></i>
      </el-button>
      <el-button
        style="position: absolute; right: 0; font-size: 25px"
        type="text"
        @click="rotateImg('right')"
      >
        <i class="el-icon-refresh-right"></i>
      </el-button>
    </template>
    <!-- 坐标 -->
    <template v-for="(ques, quesIndex) in currentRow.questions">
      <template v-if="isDrawImg && currentRow.image != ''">
        <!-- 二维码、考号、注意事项坐标 -->
        <template v-if="currentRow.page % 2 != 0">
          <div
            :style="getBoxStyle(item.pos)"
            class="page-box-main"
            v-for="(item, index) in pageBoxList"
          ></div>
        </template>
        <!-- 客观题 -->
        <div v-if="ques.is_obj">
          <template v-if="ques.type != 16">
            <points-box
            class="points-box"
            :class="{
              active: box.fill,
              error: box.fill && (Math.pow(2, optionIndex) & ques.answer_int) == 0,
              right: box.fill && (Math.pow(2, optionIndex) & ques.answer_int) != 0,
            }"
            v-for="(box, optionIndex) in ques.list"
            :ques="ques"
            :points="box"
            :quesIndex="quesIndex"
            :optionIndex="optionIndex"
            :key="optionIndex"
            :checkType="noPoint ? 'errpage' : 'detail'"
            :scale="scale"
            @choice-correct="choiceCorrect"
          ></points-box>
          </template>
        </div>
        <!-- 主观题 -->
        <div v-else>
          <div
            v-if="ques.pos && ques.pos.length && (!ques.score_list || !ques.score_list.length)"
            class="pointer-none"
            :style="{ textAlign: 'right', position: 'absolute', ...getBoxStyle(ques.pos) }"
          >
            <span class="score-text" v-if="ques.showScore">{{ ques.tempScore }}</span>
          </div>
          <template v-for="(scores, scoresIndex) in ques.score_list">
            <!-- 题目小空对错 -->
            <div
              v-if="scores.code === 0"
              :style="{
                position: 'absolute',
                ...getBoxStyle(scores.pos),
                opacity: 0.8,
                color: 'red',
                fontSize: '20px',
                fontWeight: 'bold',
                textAlign: 'right',
              }"
            >
              <span v-if="scores.score == scores.total_score">√</span>
              <span v-else>×</span>
            </div>

            <template v-for="(box, optionIndex) in scores.newArray">
              <points-box
                class="points-box"
                :ques="ques"
                :points="box"
                :quesIndex="quesIndex"
                :optionIndex="scoresIndex"
                :rowIndex="optionIndex"
                :checkType="noPoint ? 'errpage' : 'detail'"
                :scale="scale"
                @choice-correct="choiceCorrect"
              ></points-box>
            </template>
          </template>
        </div>
      </template>
    </template>
    <div
      class="stu-no-box"
      :style="{ top: 45 + 'px', left: 10 + 'px' }"
      v-if="currentRow.page % 2 != 0 && isDrawImg && currentRow.image != ''"
    >
      <span>{{ currentRow.stu_no }} </span>
    </div>

    <div v-if="defaultScore" class="total-score-title">
      <span v-show="index == 0">
        <span>总分：</span><strong>{{ defaultScore }}</strong>
      </span>
    </div>
    <template v-else>
      <template v-if="stuTableData.length == 0">
        <div class="total-score-title" v-if="isDrawImg && currentRow.image != '' && !isOnlyImg">
          <span>当前页得分</span>
        </div>
        <span class="total-score-box" v-if="!isOnlyImg">{{ totalScore }}</span>
      </template>
    </template>
    <template v-if="showQuesScore">
      <template v-for="(item, index) in currentRow.questions">
        <!-- && !item.is_obj -->
        <template v-if="item.total_score > 0 && !(item.ai_mode == 3 && source == ISOURCE_TYPES.WEB)">
          <!-- <template v-if="item.score_list?.length">
            <div
              class="score-box"
              :class="{ objBox: item.is_obj }"
              :key="index"
              :style="{
                top: (item.score_list[0]?.pos[1] + item.score_list[0]?.pos[3] - 1) * scale + 'px',
                left: (item.pos[0] + item.pos[2] - subWidth) * scale + 'px',
              }"
            >
              {{ item.tempScore }}
            </div>
          </template> -->
          <!-- <template v-else> -->
          <template>
            <div
              class="score-box"
              :key="index"
              :style="{
                top: item.pos[1] * scale + 'px',
                left: (item.pos[0] + item.pos[2] - subWidth) * scale + 'px',
              }"
            >
            <template v-if="item.scoringMode == 1">
              -{{ item.tempScore }}
            </template>
            <template v-else>
              {{ item.tempScore }}
            </template>
            </div>
          </template>
        </template>
      </template>
    </template>
  </div>
</template>

<script>
import { saveScanPageAPI, setImagePointError, setAbsentAPI } from '@/service/pexam';
import PointsBox from '@/components/scan/PointsBox.vue';
import { getScanPaperPoints } from '@/service/testbank';
import { replaceALiUrl } from '@/utils/common';
import { removeAvifParam } from '@/utils/index';
import { ISOURCE_TYPES } from '@/typings/card';
export default {
  components: { PointsBox },
  props: {
    currentRow: {
      type: Object,
      default: () => {},
    },
    pointsData: {
      type: Object,
      default: () => {},
    },
    //是否展示提交本页按钮
    isShowSubmitBtn: {
      type: Boolean,
      default: true,
    },
    // 默认旋转角度
    defaultRotate: {
      type: Number,
      default: 90,
    },
    // 默认总分数
    defaultScore: {
      type: String,
      default: '',
    },
    // 当前索引
    index: {
      type: Number,
      default: 0,
    },
    // 学生作答数据
    stuTableData: {
      type: Array,
      default: () => [],
    },

    // 是否不可点击
    noPoint: {
      type: Boolean,
      default: false,
    },
    // 多选题分数规则
    choiceScoreMap: {
      type: Object,
      default: () => {},
    },
    cardType:{
      type: Number,
      default: () => 0,
    },
    source: {
      type: String,
      default: '',
    },
  },
  watch: {
    scale(newval, oldVal) {
      // this.getScanPoints();
    },
    currentRow: {
      handler(newVal, oldVal) {
        this.totalScore = 0;
        if (newVal.questions) {
          newVal.questions.forEach(item => {
            if(item.scoringMode == 1){
              this.totalScore += Math.max((item.total_score - item.tempScore),0);
            }else{
              this.totalScore += item.tempScore;
            }
          });
        }
      },
      deep: true,
    },
    'currentRow.card_id': {
      handler(newVal, oldVal) {
        if(newVal != oldVal){
          this.getScanPoints();
        }
      },
      deep: true,
    },
  },
  data() {
    return {
      ISOURCE_TYPES: ISOURCE_TYPES,
      scale: 1,
      //答案坐标需要减去的宽度 mm
      subWidth: 20,
      isDrawImg: false,
      rotate: 90,
      drawBoxStyle: '',
      pageBoxList: [],
      examNoPoints: [],
      totalScore: 0,
      // 是否只显示图片
      isOnlyImg: false,
      //是否展示每题得分
      showQuesScore: true,
      removeAvifParam: removeAvifParam,
    };
  },
  created() {
    this.rotate = this.defaultRotate;
  },
  mounted() {
    if (!this.currentRow.questions) {
      this.isOnlyImg = true;
      return;
    }
    this.totalScore = 0;
    this.currentRow.questions.forEach(item => {
      this.totalScore += item.score;
    });
  },
  methods: {
    async getScanPoints() {
      if (this.isOnlyImg) return;
      const params = {
        paperNo: this.currentRow.card_id,
      };
      const res = await getScanPaperPoints(params);
      if (res.code == 1) {
        let pointsData = JSON.parse(res.data);
        this.pageBoxList = pointsData.pages[0].filter(item => {
          return item.item_type == 1 || item.item_type == 2 || item.item_type == 5;
        });
        const filteredItem = this.pageBoxList.find(item => item.item_type == 2);
        this.examNoPoints = filteredItem ? filteredItem.pos.map(ite => ite * this.scale) : [];
      } else {
        this.$message({
          message: '获取坐标失败！',
          type: 'error',
          duration: 1000,
        });
        return;
      }
    },
    getPoints(data) {
      this.pageBoxList = data;
    },
    getBoxStyle(box) {
      return {
        left: box[0] * this.scale + 'px',
        top: box[1] * this.scale + 'px',
        width: box[2] * this.scale + 'px',
        height: box[3] * this.scale + 'px',
      };
    },
    loadImage(val) {
      this.scale = val.currentTarget.height / 297;
      this.isDrawImg = true;
      this.getScanPoints();
      this.$emit('loadImage', val.currentTarget, this.index);
    },
    getImage(item) {
      return replaceALiUrl(item.image) + '?x-oss-process=image/resize,h_1080,image/format,jpeg';
    },

    // 获取原始图片
    getOriginImage(item) {
      let url = item.origin;
      let query = 'x-oss-process=image/rotate,' + this.rotate;
      if (url.includes('?')) {
        if(url.includes('x-oss-process')){
          return replaceALiUrl(url) + ',' + query;
        }else{
          return replaceALiUrl(url) + '&' + query;
        }
      } else {
        return replaceALiUrl(url) + '?' + query;
      }
    },
    /**
     * @name:保存修改后的数据
     * @param {*} ques  题目数据
     * @param {*} type  是否为修改考号，否：为空，是：editExamNo
     */
    submitEditQues(ques, isEditExamNo) {
      if (isEditExamNo) {
        //修改考号
        ques.code = ques.code &= ~8;
        ques.code = ques.code &= ~16;
        ques.code = ques.code &= ~32;
        ques.stu_no = this.tempStuNo;
      } else {
        //修改题目
        ques.questions.forEach(item => {
          item.status = 1;
        });
        ques.code = ques.code &= ~64;
      }
      console.log('提交题目code', ques.code);
      this.savePageData(ques);
    },
    setPointError(data) {
      let params = {
        id: data.id,
        weight: 2,
        examId: data.exam_id,
      }
      setImagePointError(params)
        .then(res => {
          this.$message({
            message: '设置成功！',
            type: 'success',
            duration: 1000,
          });
        })
        .catch(err => {
        });
    },
    openOrCloseScore() {},
    /**
     * @name:设置为缺考
     */
    setAbsent(data) {
      let params = [
        {
          id: data.id,
          isAbsent: true, //缺考true,不缺考false
        },
      ];
      setAbsentAPI(params)
        .then(res => {
          this.$message({
            message: '设置成功！',
            type: 'success',
            duration: 1000,
          });
        })
        .catch(err => {
        });
    },
    //旋转图片
    rotateImg(direction) {
      if (direction == 'right') {
        this.rotate = this.rotate == 360 ? 90 : (this.rotate += 90);
      } else {
        this.rotate = this.rotate == 0 ? 270 : (this.rotate -= 90);
      }
    },
    /**
     * @name:提交数据
     * @param {*} ques 题目数据
     */
    async savePageData(ques) {
      ques.questions.forEach(item => {
        let socre =  Math.min(item.tempScore,item.total_score);
        if(item.scoringMode == 1){
          //减分制
          socre = Math.max(item.total_score - item.tempScore,0);
        }
        item.score = socre;
      });
      const res = await saveScanPageAPI(ques);
      if (res.code == 1) {
        this.$message({
          message: '保存成功！',
          type: 'success',
          duration: 1000,
        });
      } else {
        this.$message({
          message: '保存失败！',
          type: 'error',
          duration: 1000,
        });
      }
    },
    /**
     * @name:获取处理后的题目数据
     * @param {*} currentInfo  当前题目
     */
    choiceCorrect(currentInfo) {
      let currentItem = this.currentRow.questions[currentInfo.quesIndex];
      let customNos = Object.keys(this.choiceScoreMap);
      //客观题
      if (currentItem.is_obj) {
        currentItem.status = 1;
        //当前选项
        let isCurrent = currentItem.list.filter((item, index) => {
          return index == currentInfo.optionIndex;
        })[0];
        //不是当前选项
        let notCurrent = currentItem.list.filter((item, index) => {
          return index != currentInfo.optionIndex;
        });
        isCurrent.fill = !isCurrent.fill;
        //题卡合一选做题 可选目标不超过选做数
        if(currentItem.type == 18){
          let doCount = currentItem.select_count;
          //选做一题，其他填涂区移除
          if(doCount == 1 && isCurrent.fill){
            notCurrent.forEach(ite => {
              ite.fill = false;
            });
          }else{
            //选做多题，判断是否超过选做数
            let count = currentItem.list.filter((item, index) => {
              return item.fill;
            }).length;
            if(count > doCount){
              isCurrent.fill = false;
              this.$message.error('超过可选数，请取消后重新选择');
            }
          }
        }
        if (
          currentItem.question_type &&
          (currentItem.question_type == 8 || currentItem.question_type == 2)
        ) {
          if (isCurrent.fill) {
            notCurrent.forEach(ite => {
              ite.fill = false;
            });
          }
        }
        let optionInt = 0;
        currentItem.list.forEach((item, index) => {
          if (item.fill) {
            optionInt += Math.pow(2, index);
          }
        });
        // 判断是否是多选题自定义规则
        if (customNos.includes(String(currentItem.question_no))) {
          currentItem.tempScore = this.choiceScoreMap[currentItem.question_no][optionInt] || 0;
        } else {
          // 判断是否选择了正确答案
          if (optionInt == currentItem.answer_int) {
            //全对
            currentItem.tempScore = currentItem.total_score;
          } else if (
            (optionInt & currentItem.answer_int) != 0 &&
            (optionInt & currentItem.answer_int) == optionInt
          ) {
            //半对
            currentItem.tempScore = currentItem.miss_score;
          } else {
            //全错
            currentItem.tempScore = 0;
          }
        }
      } else {
        //主观题获取分数
        currentItem.score_list[currentInfo.optionIndex].score = currentInfo.score;
        let totalScore = 0;
        currentItem.score_list.forEach(item => {
          totalScore += item.score;
        });
        currentItem.tempScore = totalScore;
      }
      this.$emit('get-change-row', this.currentRow);
    },
  },
};
</script>

<style lang="scss" scoped>
.stu-img-list {
  height: 100%;
  width: calc(100% - 305px);
  display: inline-block;
  overflow-y: scroll;
  .img {
    position: relative;
    width: 100%;
  }
}
.img-box {
  position: relative;
}
.score-box {
  position: absolute;
  width: 60px;
  height: 35px;
  // background-color: rgba(255, 61, 90, 0.218);
  opacity: 0.8;
  right: 0;
  font-size: 24px;
  text-align: center;
  // line-height: 42px;
  color: red;
  font-weight: bold;
  &.objBox {
    display: none;
  }
}
.total-score-box,
.total-score-title,
.stu-no-box {
  position: absolute;
  // background-color: rgb(255, 61, 90, 0.6);
  opacity: 0.8;
  text-align: center;
  color: red;
}
.total-score-title {
  top: 1px;
  left: 10px;
  font-size: 22px;
}
.total-score-box {
  font-size: 24px;
  top: 0px;
  left: 130px;
  font-weight: bold;
}
.stu-no-box {
  font-size: 28px;
  font-weight: bold;
}
.stu-no {
  color: #333;
  font-size: 16px;
}
.stu-task-name {
  color: #909399;
}
// .points-box {
//   position: absolute;
//   cursor: pointer;
//   z-index: 2;
//   &.active {
//     border: solid 4px #01cc7d;
//   }
//   &.error {
//     border: solid 4px red !important;
//   }
//   &.right {
//     border: solid 4px #01cc7d !important;
//   }
// }
.page-box-main {
  position: absolute;
  border: solid 2px #409eff;
}

.pointer-none {
  pointer-events: none;
}

.score-text {
  font-weight: 700;
  color: red;
  font-size: 28px;
}
.img-dropdown-menu {
  position: absolute;
  font-size: 20px;
  right: 20px;
  top: 25px;
}
.img-switch {
  position: absolute;
  font-size: 12px !important;
  right: 15px;
  top: 1px;
}
.pointer-none {
  pointer-events: none;
}
</style>
