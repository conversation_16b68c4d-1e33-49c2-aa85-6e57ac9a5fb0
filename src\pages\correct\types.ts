/*
 * @Description:
 * @Author: 小圆
 * @Date: 2025-03-26 15:28:25
 * @LastEditors: 小圆
 */

export enum SOURCE_TYPE {
  /** 教师任务批改 */
  TEACHER = 0,
  /** 按班级批改 */
  CLASS = 1,
} 

interface Linelist {
  index: number;
  score: number;
  width: string;
  quesId: string;
  answer: string[];
  points: Point[];
}

interface Point {
  page: number;
  pos: string[];
}
/**
 * @name: 教师批改题目
 */
export interface TeaCorrectQues {
  lineList: Linelist[];
  quesId: string;
  quesName: string;
  quesLevel: number;
  correctMethod: number;
  efficiencyUnCount: number;
  specialTypeId: number;
  assignType: number;
  isArbitration: boolean;
  isAiCorrect: number;
  beyondScore: number;
  isCheckScore: boolean;
  finalScore: number;
  submitStudents: number;
  completeStus: number;
  step: number;
  unCompleteStus: number;
  fixedCompleteCount: number;
  quesScore: number;
  quesType: string;
  quesList: any[];
  isCorrect: number;
  answer: string[];
  isEfficient: number;
  quesNo: string;
  tQuesNo: string;
  rightReviewed: number;
  rightNotReviewed: number;
  errorReviewed: number;
  errorNotReviewed: number;
  relateCardType: number;
  mainWorkId: string;
  abCardSheetType: number;
  /** 1:填空题智批 2：简答题智批 3：作文题智批 */
  scanMode: number;
  /** 0:纯卡 1：题卡合一 2：题卡分离 */
  cardType: number;
  points: {
    page: number;
    pos: string[];
  }[];
}

/**
 * @name: 题目批改学生
 */
export interface SmartCorrectStu {
  evaluation: string;
  quesList: QuesList[];
  similar: string;
  smartScore: SmartScore;
  stuId: string;
  correctState: number;
  proCode: string;
  teaCorrectList: any[];
  sort: number;
  procedure: string;
  stuName: string;
  stuDate: string;
  smartType: number;
  stuNewResList: StuResList[];
  stuResList: StuResList[];
  resLines: ResLine[];
  proRemark: string;
  isProblemPaper: number;
  shwId: string;
  comment: string;
  isHandleComplete: number;
  stuAnswer: string;
  evaluateState: number;
  stuScores: number;
  stuNo: string;
  correctSort: string;
  ans: {
    score: number;
    angle: string;
    content: string;
    fullScore: number;
  }[];
}

interface QuesList {
  quesId: string;
  evaluateState: number;
  comment: string;
  correctState: number;
  procedure: string;
  stuScores: number;
  quesList: any[];
  teaCorrectList: any[];
  proCode: string;
  proRemark: string;
  isHandleComplete: number;
  isProblemPaper: number;

  index: number;
  similar: string;
  stuAnswer: string;
  resId: string;
  url: string;
  correctUrl: string;
}

/**
 * @name: 学生智能得分
 */
interface SmartScore {
  conComment: string;
  wscore: number;
  wcomment: string;
  lscore: number;
  lcomment: string;
  oscore: number;
  ocomment: string;
  tscore: number;
  cscore: number;
  ctotal: number;
  wtotal: number;
  ltotal: number;
  ototal: number;
}

/**
 * @name: 学生资源列表
 */
interface StuResList {
  resId: string;
  resType: number;
  duration: number;
  source: string;
  oldSource: string;
  verticalUrl: string;
  acrossUrl: string;
  trace: string;
  correctTimeStamp: number;
  page: number;
}

interface ResLine {
  resId: string;
  quesId: string;
  url: string;
  correctUrl: string;
}

/**
 * @name: 得分选项
 */
export interface ScoreOption {
  type?: string;
  count?: number;
  score: string | number;
}

/**
 * @name: 教师评价状态
 */
export enum EVA_STATE {
  /** 未设置 */
  NOT = 0,
  /** 优秀作答 */
  WELL = 1,
  /** 典型错误 */
  WRONG = 2,
}

/**
 * @name: 复核状态
 */
export enum SMART_TYPE {
  /** 待复核 */
  WAIT_REVIEW = 0,
  /** 已复核 */
  DONE_REVIEW = 1,
}

/**
 * @description: 学生作答类型
 */
export enum PREVIEW_TYPE {
  /** 作答正确 */
  ONLY_TRUE = 2,
  /** 作答错误 */
  ONLY_ERROR = 1,
}
