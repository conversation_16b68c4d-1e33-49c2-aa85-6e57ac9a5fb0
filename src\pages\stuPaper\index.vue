<template>
  <div class="stu-paper">
    <bread-crumbs :title="'查看原卷'" @goBack="goBack">
      <template slot="titleSlot"> </template>
    </bread-crumbs>
    <div class="stu-paper-container">
      <div class="stu-paper-aside">
        <div class="aside-header">
          <div class="search-box">
            <div class="class-select-box">
              <span>当前班级：</span>
              <el-select :value="queryInfo.classId" placeholder="请选择班级" @change="switchClass">
                <el-option
                  v-for="item in classList"
                  :key="item.id"
                  :label="item.class_name"
                  :value="item.id"
                ></el-option>
              </el-select>
            </div>
            <div>
              <el-input
                v-model.trim="searchText"
                placeholder="搜索学生姓名/学号"
                prefix-icon="el-icon-search"
                clearable
                @clear="resetSearch"
                @input="searchStudent"
              ></el-input>
            </div>
          </div>
        </div>
        <div class="student-list">
          <div class="student-list-header">
            <div>姓名</div>
            <div>得分</div>
          </div>
          <div class="student-list-body" v-loading="stuLoading">
            <template v-if="filteredStudents.length">
              <div
                v-for="(item, index) in filteredStudents"
                :key="item.stuId"
                :class="['student-item', item.stuId === queryInfo.studentId ? 'active' : '']"
                @click="switchStudent(item)"
              >
                <div class="student-info">
                  <div>
                    <span class="student-name" :title="item.stuName">{{ item.stuName }}</span>
                    <span class="student-classname" :title="gradeName + classItem.class_name">{{
                      gradeName + classItem.class_name
                    }}</span>
                  </div>
                  <div class="student-no">{{ item.stuNo }}</div>
                </div>
                <div class="student-score">
                  <div class="score">{{ item.score }}分</div>
                </div>
              </div>
            </template>
            <no-data v-else></no-data>
          </div>
        </div>
      </div>
      <div class="stu-paper-main" v-loading="loading" ref="paperContent">
        <div class="stu-paper-tip" v-if="edit"><i class="el-icon-warning"></i> 所有修改记录将存档，请谨慎操作</div>

        <template v-if="stuScanData.length">
          <ImageViewer class="stu-img-viewer" ref="imageViewer">
            <div class="stu-paper-imgs-content">
              <StuPaperImg
                v-for="(item, index) in stuScanData"
                :key="item.image"
                :currentRow="item"
                :index="index"
                :defaultScore="paperScore"
                :edit="edit"
                :scoreMap="scoreMap"
                :mark="isMark"
                :source="source"
                :aiQuestions="aiQuestions"
                @showAiEvaluation="showAiEvaluation"
                @loadImage="loadImage"
              ></StuPaperImg>
            </div>

            <div class="clearfix" slot="toolLeft" v-if="isCanMark && source == 4">
              <el-button
                type="text"
                class="swiper-imageTool-comment click-element"
                :loading="btnLoading"
                :class="{ primary: isMark }"
                @click="switchMark"
              >
                笔迹痕迹
              </el-button>
            </div>

            <div class="edit-tool clearfix" slot="toolRight" v-if="editEnabled">
              <span> {{ edit ? '修改完成？' : '成绩有误？' }} </span>
              <el-button class="edit-btn" @click="switchEdit">
                <span>
                  {{ edit ? '返回查看>' : '进入修改>' }}
                </span>
              </el-button>
            </div>
          </ImageViewer>

          <div class="stu-save-box" v-if="edit">
            <div class="stu-tip-box">
              <p class="stu-tip">*当前学生修改完成请点击保存</p>
            </div>
            <p>
              <el-button type="primary" @click="save" :loading="saveLoading || publishLoading">保存</el-button>
              <el-button @click="rePublish" :loading="saveLoading || publishLoading">重新发布</el-button>
            </p>
          </div>
        </template>

        <no-data v-else-if="!loading" style="background-color: #fff; height: 100%"></no-data>
      </div>
    </div>

    <el-button v-if="editEnabled && hasAiQues" type="primary" class="ai-correct-btn" @click="goAiCorrect"
      >按题复核AI智批</el-button
    >

    <el-dialog :visible.sync="aiEvaluationVisible" width="500px" title="判分依据" top="35vh" v-dragDialog>
      <div v-if="aiEvaluation" ref="aiEvaluationRef">
        <div v-for="item in aiEvaluation.score_list">
          <span>{{ aiEvaluation.question_no }}.</span>
          <span>
            {{ item.evaluation }}
          </span>
        </div>
      </div>

      <div slot="footer" style="text-align: center">
        <el-button :disabled="aiEvaluationIndex == 0" @click="prevAiEvaluation">上一题</el-button>
        <el-button :disabled="aiEvaluationIndex == aiQuestions.length - 1" type="primary" @click="nextAiEvaluation"
          >下一题</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts">
import {
  getExamAnalyzeState,
  getExamInfoAPI,
  getMergeStatus,
  getNewReportCard,
  getPublishScoreStateAPI,
  getStuQuesAnalyze,
  getStuScanDataAPI,
} from '@/service/pexam';
import { Component, Mixins, Ref, Vue } from 'vue-property-decorator';
import {
  getExamClassStuListAPI,
  getPublicConfigBySchoolInfo,
  getTeaCorrectQuesList,
  publishScoreAPI,
  saveHandReadStuExamAPI,
} from '@/service/api';
import { GetScoreMap, getStudentPaperImage } from '@/service/xueban';
import DochoiceMixin from '../lookReport/mixin/DochoiceMixin.vue';
import BreadCrumbs from '@/components/Breadcrumbs/index.vue';

import ImageViewer from './components/ImageViewer.vue';
import NoData from '@/components/noData.vue';
import StuPaperImg from '@/components/SwiperViewer/StuPaperImg.vue';
import { ISOURCE_TYPES } from '@/typings/card';
import { IPaper } from '@/components/SwiperViewer/StuPaperInterface';
import { Loading } from '@iclass/element-ui';
import { isEqual } from 'xe-utils';
import { ScoreStatusManager } from '@/utils/examReportUtils';

interface Stu {
  id: string;
  stuId: string;
  stuName: string;
  stuNo: string;
  abTestPaperType: number;
  source: number;
  score: number;
  paperNo: string;
}

// 深拷贝函数
function deepClone(obj) {
  if (obj === null || typeof obj !== 'object') {
    return obj;
  }
  if (Array.isArray(obj)) {
    const newArr = [];
    for (const item of obj) {
      newArr.push(deepClone(item));
    }
    return newArr;
  }

  let newObj = {};
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      newObj[key] = deepClone(obj[key]);
    }
  }
  return newObj;
}

@Component({
  components: {
    StuPaperImg,
    ImageViewer,
    NoData,
    BreadCrumbs,
  },
})
export default class StuPaper extends Mixins(DochoiceMixin) {
  ISOURCE_TYPES = ISOURCE_TYPES;
  @Ref('paperContent') paperContent: HTMLDivElement;
  @Ref('imageViewer') imageViewer: ImageViewer;

  // 查询参数
  queryInfo = {
    classId: this.$route.query.classId as string,
    subjectId: this.$route.query.subjectId as string,
    studentId: this.$route.query.studentId as string,
    studentNo: this.$route.query.studentNo as string,
    workId: this.$route.query.workId as string,
    examId: this.$route.query.examId as string,
    abPaper: this.$route.query.abPaper as string,
    source: this.$route.query.source as string,
  };

  get source() {
    if (this.stuItem) {
      return this.stuItem.source;
    } else {
      return this.queryInfo.source;
    }
  }

  stuScanData: IPaper[] = [];
  tempStuScanData: IPaper[] = [];
  hasMark = false;
  loading = false;
  isMark = false;
  btnLoading = false;
  isCanMark = false;

  // 班级列表
  classList = [];
  // 当前班级
  get classItem() {
    return this.classList.find(item => item.id == this.queryInfo.classId);
  }
  // 年级名称
  gradeName = '';
  // 学生列表
  stuList: Stu[] = [];
  // 当前筛选的学生列表
  filteredStudents: Stu[] = [];
  // 当前学生
  get stuItem() {
    return this.stuList.find(item => item.stuId == this.queryInfo.studentId);
  }
  // 搜索字段
  searchText = '';
  // 是否编辑模式
  edit = false;
  // 学生列表加载状态
  stuLoading = true;

  // 试卷号分数映射
  paperNoScoreMap = {};
  // 分数映射
  scoreMap = {};

  // 是否正在发布成绩
  publishLoading = false;
  // 是否正在保存
  saveLoading = false;

  // 检查成绩状态定时器
  checkTimer = null;
  // 检查成绩状态次数
  checkCount = 0;
  // 检查成绩状态loading
  checkLoading = null;
  // 智批改题目列表
  correctQuesList = [];
  // AI题目列表
  aiQuestions = [];
  // AI评分依据
  aiEvaluation = null;
  // 当前AI评分依据索引
  aiEvaluationIndex = 0;
  // AI评分依据弹窗显示状态
  aiEvaluationVisible = false;

  get isParentReport() {
    return this.$sessionSave.get('reportDetail').examId == this.$sessionSave.get('reportParent').examId;
  }

  get paperScore() {
    if (this.edit) {
      let score = 0;
      this.stuScanData.forEach(item => {
        item.questions.forEach(ques => {
          score += ques.score < 0 ? 0 : ques.score ?? 0;
        });
      });
      return score;
    } else {
      return this.stuItem?.score;
    }
  }

  // 是否可编辑
  get editEnabled() {
    return (
      this.source == String(ISOURCE_TYPES.HAND) &&
      (this.isParentReport || this.$sessionSave.get('reportDetail').source == 105)
    );
  }

  // 是否有智批题
  get hasAiQues() {
    return this.stuScanData.some(item => item.questions.some(ques => ques.ai_mode)) && this.correctQuesList.length > 0;
  }

  async mounted() {
    this.classList = (this.$sessionSave.get('innerClassList') || []).filter(item => item.id);
    this.gradeName = this.$sessionSave.get('reportDetail').gradeName;
    await this.getStuList();
    this.getCorrectQuesList();
    this.getScoreMap();
    this.getStuPaper();
    this.getMarkConfig();
    document.querySelector('.student-item.active')?.scrollIntoView({ behavior: 'smooth' });
  }

  // 获取题目列表
  async getCorrectQuesList() {
    if (this.source != ISOURCE_TYPES.HAND) return;
    this.correctQuesList = [];
    const params: any = {
      workId: this.$route.query.workId,
      userId: this.$sessionSave.get('loginInfo').id,
      schoolId: this.$sessionSave.get('schoolInfo').id,
      smartCorrect: 1, // 智批改0：主观题阅卷 1：智批改阅卷
      correctVersion: 4,
    };
    if (this.$route.query.classId) {
      params.classId = this.$route.query.classId;
      params.source = 1;
    }
    const { code, data, msg } = await getTeaCorrectQuesList(params);
    if (code !== 1 || (data && data.length == 0)) {
      return;
    }
    this.correctQuesList = data;
  }

  // 获取学生列表
  async getStuList() {
    this.stuLoading = true;
    const res = await getExamClassStuListAPI({
      examId: this.queryInfo.examId,
      classId: this.queryInfo.classId,
      subjectId: this.queryInfo.subjectId,
    });
    this.stuList = res.data || [];
    this.searchStudent();
    this.stuLoading = false;
  }

  // 搜索学生
  searchStudent() {
    if (!this.searchText) {
      this.filteredStudents = [...this.stuList];
    } else {
      const keyword = this.searchText.toLowerCase();
      this.filteredStudents = this.stuList.filter(
        stu => stu.stuName.toLowerCase().includes(keyword) || stu.stuNo.toLowerCase().includes(keyword)
      );
    }
  }

  // 重置搜索
  resetSearch() {
    this.searchText = '';
    this.filteredStudents = [...this.stuList];
  }

  // 切换班级
  async switchClass(id) {
    await this.checkEdit('class');
    this.queryInfo.classId = id;
    this.tempStuScanData = [];
    this.stuScanData = [];
    await this.getStuList();
    this.switchStudent(this.stuList[0]);
    this.getCorrectQuesList();
  }

  // 切换学生
  async switchStudent(student: Stu) {
    await this.checkEdit('student');

    this.queryInfo.studentId = student.stuId;
    this.queryInfo.studentNo = student.stuNo;

    if (this.stuItem.source == ISOURCE_TYPES.WEB) this.edit = false;

    this.hasMark = false;
    this.isMark = false;

    this.getScoreMap();
    this.getStuPaper();
  }

  async switchEdit() {
    await this.checkEdit('edit');
    if (this.edit) this.stuScanData = deepClone(this.tempStuScanData);
    this.edit = !this.edit;
  }

  // 获取学生试卷
  async getStuPaper() {
    this.loading = true;
    this.aiQuestions = [];

    try {
      const scanRes = await getStuScanDataAPI({
        examId: this.queryInfo.workId,
        stuNo: this.queryInfo.studentNo,
        stuId: this.queryInfo.studentId,
      });
      if (scanRes.code != 1) return;
      const stuQuesAnalyzeRes = await getStuQuesAnalyze({
        examId: this.$route.query.examId,
        classId: this.queryInfo.classId,
        subjectId: this.queryInfo.subjectId,
        studentId: this.queryInfo.studentId,
        abPaper: this.queryInfo.abPaper,
      });
      if (stuQuesAnalyzeRes.code != 1) return;

      let scanData = scanRes.data;
      let stuQuesAnalyzeData = stuQuesAnalyzeRes.data;

      const quesMap = {};
      stuQuesAnalyzeData.forEach(item => {
        let no = item.sort;
        if (quesMap[no]) {
          if (Array.isArray(quesMap[no])) {
            quesMap[no].push(item);
          } else {
            quesMap[no] = [quesMap[no], item];
          }
        } else {
          quesMap[no] = item;
        }
      });

      await this.setQuesBlock(scanData);

      scanData.forEach(scanItem => {
        this.$set(scanItem, 'isEdit', false);
        if (scanItem.ai_questions) {
          scanItem.ai_questions.forEach(aiItem => {
            if (aiItem.score_list.some(t => t.evaluation)) {
              this.aiQuestions.push(aiItem);
            }
          });
        }
        scanItem.questions.forEach(item => {
          this.$set(item, 'showScore', false);
          this.$set(item, 'tempScore', item.score);
          // 跳过选做题
          if (item.type == 18) return;

          if (Reflect.has(quesMap, String(item.question_no))) {
            const analyzeQues = quesMap[String(item.question_no)];
            if (Array.isArray(analyzeQues)) {
              const sumScore = analyzeQues.map(item => item.score).join(',');
              this.$set(item, 'showScore', sumScore !== null);
              this.$set(item, 'tempScore', sumScore || 0);
              this.$set(item, 'tempTitle', analyzeQues.map(item => item.quesNo).join(','));
              this.$set(
                item,
                'tempScoreList',
                analyzeQues.map(t => {
                  return {
                    score: t.score,
                    title: t.quesNo,
                  };
                })
              );
            } else {
              this.$set(
                item,
                'showScore',
                analyzeQues.fullScore !== 0 && analyzeQues.score !== null && analyzeQues.chooseType !== '0'
              );
              this.$set(item, 'tempScore', analyzeQues.score || 0);
              this.$set(item, 'tempTitle', analyzeQues.quesNo);
              this.$set(item, 'tempScoreList', [{ score: analyzeQues.score, title: analyzeQues.quesNo }]);
            }

            delete quesMap[String(item.question_no)];
          }
        });
      });
      this.stuScanData = scanData;
    } catch (error) {
      console.error(error);
      this.stuScanData = [];
    } finally {
      this.loading = false;
    }

    this.tempStuScanData = deepClone(this.stuScanData);
  }

  // 设置题块
  async setQuesBlock(stuScanData) {
    let stuDoQuesList = await this.getStuElectiveQues({
      schoolId: this.$sessionSave.get('schoolInfo').id,
      examId: this.queryInfo.examId as string,
      subjectId: this.queryInfo.subjectId as string,
      stuId: this.queryInfo.studentId as string,
    });

    let chooseQuesList = stuDoQuesList.filter(item => {
      return item.chooseType == 1;
    });

    for (const item of stuScanData) {
      for (const ques of item.questions) {
        for (const chooseQues of chooseQuesList) {
          // 对应题块位置更新所选题目
          if (chooseQues.targetId == ques.question_id) {
            ques.question_no = chooseQues.quesNo;
          }
        }
      }
    }
  }

  // 获取是否开启画笔留痕（中控配置项）
  async getMarkConfig() {
    let code = '119';
    let flag = 0;
    try {
      const { data } = await getPublicConfigBySchoolInfo({
        schoolId: this.$sessionSave.get('schoolInfo').id || this.$sessionSave.get('loginInfo').schoolid,
        dictCode: code,
        userId: this.$sessionSave.get('loginInfo').id,
      });
      flag = Number(data[0].state) || 0;
    } catch (error) {
      console.error(error);
      flag = 0;
    }
    this.isCanMark = !!flag;
  }

  // 笔迹痕迹
  async switchMark() {
    if (!this.hasMark) {
      await this.getMarkImage();
    }
    if (this.hasMark) this.isMark = !this.isMark;
  }

  // 获取批改痕迹图片
  async getMarkImage() {
    this.btnLoading = true;
    this.loading = true;
    try {
      const res = await getStudentPaperImage({
        examId: this.queryInfo.workId,
        studentNo: this.queryInfo.studentNo,
        showScore: 0,
      });
      if (res.data && res.data.length) {
        res.data.forEach((item, i) => {
          this.stuScanData[i].markImage = item;
        });
        this.hasMark = true;
      }
    } catch (error) {
      console.error(error);
    } finally {
      this.btnLoading = false;
      this.loading = false;
    }
  }

  // 加载图片
  loadImage(e: HTMLImageElement, index: number) {
    this.imageViewer.resetZoom(false);
  }

  // 保存
  async save() {
    this.saveLoading = true;
    const params = {
      examId: this.queryInfo.examId,
      subjectId: this.queryInfo.subjectId,
      stuId: this.stuItem.stuId,
      teaId: this.$sessionSave.get('loginInfo').id,
      teaName: this.$sessionSave.get('loginInfo').realname,
      quesList: this.getQuesList(),
      source: 1, // 0: 阅卷智批, 1: 学情智批
    };
    try {
      const res = await saveHandReadStuExamAPI(params);
      this.$message.success('保存成功');
      await this.getStuList();
      this.tempStuScanData = deepClone(this.stuScanData);
      document.querySelector('.student-item.active')?.scrollIntoView({ behavior: 'smooth' });
    } catch (error) {
      console.error(error);
    } finally {
      this.saveLoading = false;
    }
  }

  /** 合并数据 */
  async mergeData() {
    const mergeLoading = Loading.service({
      lock: true,
      text: '正在合并数据，请稍候',
      background: 'rgba(0, 0, 0, 0.7)',
    });

    try {
      let mergeTimes = 0;
      while (mergeTimes < 20) {
        const res = await getMergeStatus({
          examId: this.queryInfo.workId,
          times: mergeTimes,
        });

        if (res.data.status === 0) {
          mergeLoading.close();
          return;
        }

        await sleep(1000);
        mergeTimes++;
      }
    } catch (error) {
      console.error(error);
      mergeLoading.close();
      this.$message.error('合并数据失败');
      this.publishLoading = false;
      throw error;
    }
  }

  /** 发布成绩 */
  async rePublish() {
    await this.$confirm('重新发布将重新统计学情，确定重新发布吗?<br />请确认全部学生修改完成且保存', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
      dangerouslyUseHTMLString: true,
    });
    this.publishLoading = true;

    try {
      const res = await publishScoreAPI({
        schoolId: this.$sessionSave.get('schoolInfo').id,
        workId: this.queryInfo.workId,
      });
      if (res.code == 1) {
        const scoreStatusManager = new ScoreStatusManager();
        const check = await scoreStatusManager.startCheck(this.queryInfo.examId);
        if (check === 'success') {
          this.$message.success('发布成绩成功');
          this.getStuPaper();
        } else if (check === 'timeout') {
          this.$message.error('发布成绩超时，请稍后再试');
        } else {
          this.$message.error('发布成绩失败');
        }
      } else {
        this.$message.error(res.msg || '发布成绩失败');
      }
    } catch (error) {
      console.error(error);
    } finally {
      this.publishLoading = false;
    }
  }

  // 获取题目列表
  getQuesList() {
    const quesList = [];
    this.stuScanData.forEach(item => {
      item.questions.forEach(quesItem => {
        const quesParams = {
          page: item.page,
          quesNo: quesItem.question_no,
          score: quesItem.score ?? 0,
          isRight: quesItem.score == quesItem.total_score,
          answer: null,
          scoreList: quesItem.score_list ? quesItem.score_list.map(t => t.score) : null,
        };

        if (quesItem.is_obj) {
          quesParams.answer = quesItem.list
            .map((t, i) => (t.fill ? String.fromCharCode(65 + i) : ''))
            .filter(t => t)
            .join(',');
        }

        if (quesItem.type == 18) {
          return;
        }

        quesList.push(quesParams);
      });
    });
    return quesList;
  }

  // 获取分数映射
  async getScoreMap() {
    try {
      const paperNo = this.stuItem.paperNo;
      if (this.paperNoScoreMap[paperNo]) {
        this.scoreMap = this.paperNoScoreMap[paperNo];
      } else {
        const res: any = await GetScoreMap({ paper_no: paperNo });
        if (res.code == 1) {
          this.scoreMap = res.data || {};
          this.paperNoScoreMap[paperNo] = this.scoreMap;
        }
      }
    } catch (error) {
      this.scoreMap = {};
    }
  }

  // 检查编辑返回
  async checkEdit(type: 'class' | 'student' | 'back' | 'edit') {
    let msg = '';
    if (type == 'class') {
      msg = '切换班级';
    } else if (type == 'student') {
      msg = '切换学生';
    } else if (type == 'back') {
      msg = '返回';
    } else if (type == 'edit') {
      msg = '返回查看';
    }

    if (this.edit) {
      if (!isEqual(this.tempStuScanData, this.stuScanData)) {
        await this.$confirm(`修改尚未保存，确定${msg}吗?<br />${msg}后当前学生修改记录将被清空`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          dangerouslyUseHTMLString: true,
        });
      }
    }
  }

  // 返回
  async goBack() {
    await this.checkEdit('back');
    this.$router.back();
  }

  // 按题复核AI智批
  goAiCorrect() {
    this.$router.push({
      path: '/correct',
      query: {
        classId: this.queryInfo.classId,
        workId: this.queryInfo.workId,
        subjectId: this.queryInfo.subjectId,
        source: '1', // 1：按班级批改
      },
    });
  }

  // 显示AI判分依据
  async showAiEvaluation(ques) {
    this.aiEvaluation = null; // 先清空，防止切换题目时，上一个题目的判分依据公式还在
    await this.$nextTick();
    const aiItemIndex = this.aiQuestions.findIndex(t => t.question_id == ques.question_id);
    this.aiEvaluationIndex = aiItemIndex;
    this.aiEvaluation = this.aiQuestions[aiItemIndex];
    this.aiEvaluationVisible = true;

    this.$nextTick(() => {
      setTimeout(() => {
        MathJax.typesetPromise([this.$refs.aiEvaluationRef]);
      }, 0);
    });
  }

  // 上一题
  async prevAiEvaluation() {
    this.aiEvaluation = null;
    await this.$nextTick();
    this.aiEvaluationIndex--;
    this.aiEvaluation = this.aiQuestions[this.aiEvaluationIndex];
    this.$nextTick(() => {
      setTimeout(() => {
        MathJax.typesetPromise([this.$refs.aiEvaluationRef]);
      }, 0);
    });
  }

  // 下一题
  async nextAiEvaluation() {
    this.aiEvaluation = null;
    await this.$nextTick();
    this.aiEvaluationIndex++;
    this.aiEvaluation = this.aiQuestions[this.aiEvaluationIndex];
    this.$nextTick(() => {
      setTimeout(() => {
        MathJax.typesetPromise([this.$refs.aiEvaluationRef]);
      }, 0);
    });
  }
}
</script>

<style scoped lang="scss">
.stu-paper {
  width: 100%;
  height: 100%;
  font-size: 14px;
}

.stu-paper-container {
  display: flex;
  height: calc(100% - 40px);
  gap: 10px;
}

.stu-paper-aside {
  flex: none;
  width: 260px;
  display: flex;
  flex-direction: column;
  background-color: #f5f7fa;
  overflow: hidden;
  border-radius: 10px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  .aside-header {
    padding: 16px;
    border-bottom: 1px solid #e6e6e6;

    .search-box {
      color: #606266;

      .class-select-box {
        display: flex;
        align-items: center;
        .el-select {
          flex: 1;
        }
      }

      .el-input {
        margin-top: 8px;
        width: 100%;
      }
    }

    .sort-box {
      width: 100%;

      .el-select {
        width: 100%;
      }
    }
  }

  .student-list {
    display: flex;
    flex-direction: column;
    flex: 1;
    overflow-y: auto;
    padding: 8px 0;

    .student-list-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 16px;
      border-bottom: 1px solid #f0f0f0;
      color: #909399;
      font-weight: bold;
      font-size: 14px;
    }

    .student-list-body {
      flex: 1;
      overflow-y: auto;
    }

    .student-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 16px;
      cursor: pointer;
      border-bottom: 1px solid #f0f0f0;
      transition: all 0.05s;

      &:hover {
        background-color: #ecf5ff;
      }

      &.active {
        background-color: #ecf5ff;
        border-left: 4px solid #409eff;
      }

      .student-info {
        flex: 1;

        .student-name {
          font-size: 14px;
          font-weight: bold;
          color: #303133;
          margin-bottom: 4px;
        }

        .student-classname {
          font-size: 14px;
          color: #909399;
          margin-left: 16px;
        }

        .student-no {
          font-size: 12px;
          color: #909399;
        }
      }

      .student-score {
        text-align: right;

        .score {
          font-size: 16px;
          font-weight: bold;
          color: #f56c6c;
          margin-bottom: 4px;
        }
      }
    }
  }
}

.stu-paper-main {
  position: relative;
  flex: 1;
  height: 100%;
  // background-color: #bdc3c7;
  border-radius: 10px;

  overflow: auto;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.stu-paper-tip {
  position: absolute;
  top: 4px;
  right: 4px;
  font-size: 16px;
  font-weight: bold;
  color: #f56c6c;
  z-index: 10;
}

.mark-control {
  padding: 16px;
  display: flex;
  justify-content: center;
  border-top: 1px solid #e6e6e6;
}

.stu-img-viewer {
  // background-color: #e5e5e5;
  border-radius: 10px;
  user-select: none;
}

.stu-paper-imgs-content {
  display: inline-block;
  white-space: nowrap;
  background: #ccc;
  border-radius: 10px;
}

.stu-edit-box {
  position: fixed;
  bottom: 155px;
  right: 30px;
  z-index: 10;
  font-size: 16px;
  background-color: rgba(255, 255, 255, 0.9);
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;

  p {
    margin: 0 0 10px;
    font-weight: 500;
    color: #606266;
  }
}

.edit-tool {
  padding: 0 4px;
  display: flex;
  align-items: center;
  font-size: 20px;
  color: #fff;
}

.edit-btn {
  padding: 8px 16px;
  border-radius: 4px;
  background: #f5f7fa;
  transition: all 0.3s;
  font-size: 20px;
  border: 1px solid #409eff;
  color: #409eff;

  &:hover {
    background: #ecf5ff;
    border-color: #c6e2ff;
  }
}

.edit-icon {
  width: 20px;
  height: 20px;
  margin-right: 6px;
  vertical-align: middle;
}

.stu-save-box {
  position: fixed;
  min-width: 225px;
  bottom: 10px;
  right: 10px;
  z-index: 10;
  background-color: rgba(255, 255, 255, 0.9);
  padding: 15px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;

  .stu-tip-box {
    color: #f56c6c;
    font-size: 13px;
    margin-bottom: 12px;

    .stu-tip {
      margin: 0 0 5px;
      line-height: 1.5;
      display: flex;
      align-items: center;
    }
  }

  p {
    margin: 0;
    display: flex;
    gap: 10px;
  }

  .el-button {
    padding: 10px 20px;
  }
}

.swiper-imageTool-comment {
  float: left;
  // background: rgba(0, 0, 0, 0.5);
  padding: 0 10px;
  height: 60px;
  overflow: hidden;
  font-size: 24px;
  color: #fff;
  line-height: 60px;

  &-left {
    margin-right: 10px;
  }

  &-right {
    margin-left: 10px;
  }

  &-comment {
    padding: 0 20px;
  }

  &.primary,
  &.active {
    background-color: #3e73f6;
  }

  &.tool-block--add {
    background-color: #3e73f6;
  }

  &.tool-block--remove {
    background-color: #a8b0c6;
  }
}

.ai-correct-btn {
  position: absolute;
  top: 0;
  left: 270px;
}
</style>
