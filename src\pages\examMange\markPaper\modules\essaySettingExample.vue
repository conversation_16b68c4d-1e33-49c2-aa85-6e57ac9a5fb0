<template>
  <div class="essay-setting-example">
    <h2>作文设置组件使用示例</h2>
    
    <!-- 基础使用 -->
    <div class="example-section">
      <h3>基础使用（显示图片区域）</h3>
      <essay-setting
        :aiEssayQuesData="mockQuesData"
        :isReadonly="false"
        :hasStyle="true"
        :showImages="true"
        :subjectId="1"
        @saveAICorrectQue="handleSave"
      />
    </div>
    
    <!-- 只读模式 -->
    <div class="example-section">
      <h3>只读模式（隐藏图片区域）</h3>
      <essay-setting
        :aiEssayQuesData="mockQuesData"
        :isReadonly="true"
        :hasStyle="true"
        :showImages="false"
        :subjectId="1"
        @saveAICorrectQue="handleSave"
      />
    </div>
  </div>
</template>

<script>
import EssaySetting from './essaySetting.vue';

export default {
  name: 'EssaySettingExample',
  components: {
    EssaySetting
  },
  data() {
    return {
      // 模拟题目数据
      mockQuesData: [
        {
          id: 1,
          quesNo: 'Q001',
          quesNos: '第1题',
          score: 25,
          way: 1,
          question: '请根据以下材料写一篇英语作文...',
          corrRule: '语法正确，内容完整，逻辑清晰...',
          hasTip: false
        },
        {
          id: 2,
          quesNo: 'Q002', 
          quesNos: '第2题',
          score: 25,
          way: 2,
          question: '阅读下面材料，然后续写...',
          corrRule: '续写合理，语言流畅...',
          hasTip: true
        }
      ]
    };
  },
  methods: {
    handleSave() {
      console.log('保存作文设置');
      this.$message.success('作文设置保存成功');
    }
  }
};
</script>

<style lang="scss" scoped>
.essay-setting-example {
  padding: 20px;
  
  .example-section {
    margin-bottom: 40px;
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    padding: 20px;
    
    h3 {
      margin-top: 0;
      margin-bottom: 20px;
      color: #303133;
      border-bottom: 1px solid #e4e7ed;
      padding-bottom: 10px;
    }
  }
}
</style>
