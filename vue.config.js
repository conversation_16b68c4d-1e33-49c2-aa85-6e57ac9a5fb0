/*
 * @Description: 
 * @Author: 小圆
 * @Date: 2024-01-29 13:50:13
 * @LastEditors: 小圆
 */
"use strict";
const port = process.env.port || process.env.npm_config_port || 1001; // dev port
const name = "";
const path = require("path");
const CompressionWebpackPlugin = require('compression-webpack-plugin')
const fs = require('fs');
const { WebUpdateNotificationPlugin } = require("@plugin-web-update-notification/webpack");
// All configuration item explanations can be find in https://cli.vuejs.org/config/
module.exports = {
  publicPath: process.env.VUE_APP_BASE_URL,
  outputDir: "dist",
  assetsDir: "static",
  lintOnSave: false, //process.env.NODE_ENV === 'development',
  productionSourceMap: false,
  devServer: {
    port: port,
    open: false,
    overlay: {
      warnings: false,
      errors: true,
    },
    proxy: {
      "/aliba": {
        target: "https://fs.iclass30.com/",
        ws: true,
        changeOrigin: true,
      },
      "/ptask": {
        target: "http://127.0.0.1:9801/",
        ws: true,
        changeOrigin: true,
      },
      "/pbook": {
        target: "http://127.0.0.1:9800/",
        ws: true,
        changeOrigin: true,
      },
      "/pexam": {
        target: "http://127.0.0.1:9806/",
        ws: true,
        changeOrigin: true,
      },
      "/eReport": {
        target: "http://127.0.0.1:9999/",
        ws: true,
        changeOrigin: true,
      },
    },
  },

  chainWebpack: (config) => {
    config.optimization.minimizer('terser').tap((args) => {
      args[0].terserOptions.compress = {
        ...args[0].terserOptions.compress,
        pure_funcs: [
          'console.debug',
          'console.table',
        ],
      }

      args[0].terserOptions.output = {
        comments: false,
      }
      args[0].extractComments = false
      return args
    })

    config.resolve.alias
      .set('@', resolve('src'))
      .set('element-ui', resolve('node_modules/@iclass/element-ui'))

    config.plugin("html").tap((args) => {
      args[0].title = name;
      return args;
    });

    if (process.env.use_analyzer) { 
      config
        .plugin('webpack-bundle-analyzer')
        .use(require('webpack-bundle-analyzer').BundleAnalyzerPlugin)
    }
    config.optimization.splitChunks({
      chunks: 'all', 
      cacheGroups: {
        vendors: {
          name: 'chunk-vendors',
          test: /[\\/]node_modules[\\/]/,
          priority: -10,
          chunks: 'initial'
        },
        // 单独打包大型库
        elementUI: {
          name: 'chunk-elementui',
          priority: 20,
          test: /[\\/]node_modules[\\/]element-ui[\\/]/,
          chunks: 'all'
        },
        echarts: {
          name: 'chunk-echarts',
          priority: 20,
          test: /[\\/]node_modules[\\/]echarts[\\/]/,
          chunks: 'all'
        },
        lodash: {
          name: 'chunk-lodash',
          priority: 20,
          test: /[\\/]node_modules[\\/]lodash[\\/]/,
          chunks: 'all'
        }
      }
    })
  },
  css: {
    loaderOptions: {
      sass: {
        sassOptions: { outputStyle: "expanded" },
      },
    },
  },
  configureWebpack: (config) => {
    if (process.env.VUE_APP_CHANNEL !== 'c30') {
      config.plugins.push({
        apply: (compiler) => {
          compiler.hooks.done.tap('DeleteFilePlugin', () => {
            const filePath = path.resolve(__dirname, 'dist/favicon.ico');
            if (fs.existsSync(filePath)) {
              fs.unlinkSync(filePath);
              console.log('已删除文件:', filePath);
            }
          });
        }
      });
    }

    // 压缩插件
    config.plugins.push(
      new CompressionWebpackPlugin({
        algorithm: 'gzip',
        test: /\.(js|css)$/,
        threshold: 10240,
        minRatio: 0.8
      })
    );

    // 版本检测插件
    config.plugins.push(
      new WebUpdateNotificationPlugin({
        checkInterval: 10 * 60 * 1000, // 10分钟检查一次
        notificationProps: {
          title: '📢 系统更新',
          description: '检测到新版本，请刷新页面获取最新功能',
          buttonText: '立即刷新',
          dismissButtonText: '稍后再说'
        },
        silence: false, // 静默更新设为true：部署后就不会显示更新提示了
        // 自定义更新检测逻辑
        onUpdateFound: (version, oldVersion) => {
        }
      })
    )
  }
};


function resolve(dir) {
  return path.join(__dirname, dir);
}
