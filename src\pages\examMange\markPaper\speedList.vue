<template>
  <div class="allocate-container">
    <el-page-header @back="backReport" content="阅卷监控"> </el-page-header>
    <div class="allocate-content">
      <!--左侧题目列表-->
      <div class="marking-progress-area">
        <!--头部-->
        <div class="marking-progress-area-header">
          <div class="paper-title" :title="totalDataObj.examTitle + totalDataObj.subjectName">
            {{ totalDataObj.examTitle }}（{{ totalDataObj.subjectName }}）
          </div>
          <div class="data-area">
            <div class="data-item">
              参考人数：<span class="sub-number">{{ totalDataObj.AllSubmitStus }}</span>
            </div>
            <div class="data-item">
              缺考人数：<span class="send-number">{{ totalDataObj.notAnswerNum }}</span>
              <span style="position: absolute; top: 2px">
                <el-popover placement="top-start" title="" trigger="hover">
                  <div>含标记缺考和未扫</div>
                  <i class="el-icon-question" slot="reference"></i>
                </el-popover>
              </span>
            </div>
            <div class="data-item">
              总人数：<span class="total-number">{{ totalDataObj.AllsendStudents }}</span>
            </div>
          </div>
        </div>
        <div class="end-marking-button" v-if="(isAdminorOperate || isSubjectLeader) && checkType == 'speed'">
          <!-- 备课组长下载问题卷 -->
          <el-button @click="downLoadExcel" style="margin-right: 20px">下载问题卷名单</el-button>
          <el-button type="primary" @click="endExamMark" :disabled="totalDataObj.isOver != 0">{{
      totalDataObj.isOver == 0 ? '结束阅卷' : '已结束'
    }}</el-button>
        </div>
        <el-radio-group v-model="checkType" class="check-type-radio" @change="changeCheckType">
          <el-radio-button label="speed">阅卷进度</el-radio-button>
          <el-radio-button label="quality">阅卷质量</el-radio-button>
          <el-radio-button label="object">客观题明细</el-radio-button>
        </el-radio-group>
        <!--阅卷进度-->
        <template v-if="checkType == 'speed'">
          <el-tabs v-model="smartCorrect" @tab-click="handleClick" class="correct-tab">
            <el-tab-pane label="主观题批改" name="0">
              <div class="marking-progress-area-filter">
                仅显示已阅教师
                <el-switch v-model="isOnlyCorrectTea" @change="handleClick" active-color="#409EFF"
                  inactive-color="#DCDFE6">
                </el-switch>
              </div>
              <div class="marking-progress-area-body">
                <el-row class="table-header">
                  <el-col :span="3" class="col-li"> 题块</el-col>
                  <el-col :span="2" class="col-li"> 评阅方式</el-col>
                  <el-col :span="2" class="col-li"> 分配方式</el-col>
                  <el-col :span="5" class="col-li">
                    整体进度
                    <el-popover placement="top-start" title="" trigger="hover">
                      <div style="line-height: 25px; font-size: 13px">
                        整体进度=（已阅-问题卷剩余-仲裁卷剩余）/ 总任务量*100%<br />
                      </div>
                      <i class="el-icon-question" slot="reference"></i>
                    </el-popover>
                  </el-col>
                  <el-col :span="3" class="col-li"> 阅卷老师 </el-col>
                  <el-col :span="3" class="col-li"> 已阅/总任务量 </el-col>
                  <el-col :span="2" class="col-li"> 剩余任务量 </el-col>
                  <el-col :span="2" class="col-li ques-col">
                    <span>问题卷
                      <el-popover placement="top-start" title="" trigger="hover">
                      <div style="line-height: 25px; font-size: 13px">
                        由题组长处理，在阅卷模块即可查看
                      </div>
                      <i class="el-icon-question" slot="reference"></i>
                    </el-popover>
                    </span>
                    <span>剩余/总量</span>
                  </el-col>
                  <el-col :span="2" class="col-li ques-col">
                    <span>仲裁卷
                      <el-popover placement="top-start" title="" trigger="hover">
                      <div style="line-height: 25px; font-size: 13px">
                        由仲裁老师处理，在阅卷模块即可查看
                      </div>
                      <i class="el-icon-question" slot="reference"></i>
                    </el-popover>
                    </span>
                    <span>剩余/总量</span>
                  </el-col>
                </el-row>
                <!--内容-->
                <div class="content-height progress-height" v-if="examMarkDetailsList.length > 0">
                  <template v-for="(item, index) in examMarkDetailsList">
                    <el-row class="table-content" :style="{ height: item.rows.length * 45 + 'px' }">
                      <el-col :span="3" class="col-li summary-col" style="word-break: break-all;flex-wrap: wrap;"
                        :title="item.quesName">
                        <div>{{
      item.quesName.length > 20 * item.rows.length
        ? item.quesName.substring(0, 20 * item.rows.length) + '...'
        : item.quesName
    }}</div>
                        <div v-if="item.isChooseDo" style="color: #409EFF;width: 100%;">选做题（{{ item.chooseName.join(',')
                          }}）</div>
                      </el-col>
                      <el-col :span="2" class="col-li">{{ isAIDoubleEval(item) ? '人机双评' : (item.correctMode == 1 ?
                    '单评' : '双评') }}</el-col>
                      <el-col :span="2" class="col-li">{{ getAssignName(item.assignType) }}</el-col>
                      <el-col :span="5" class="col-li summary-col">
                        <el-progress :color="item.overall == 100 ? '#67C23A' : '#409EFF'" class="summary-progress"
                          :percentage="item.overall"></el-progress>
                      </el-col>
                      <el-col :span="12">
                        <template v-for="(subItem, subIndex) in item.rows">
                          <el-row style="width: 100%; height: 45px; line-height: 45px">
                            <el-col :span="6" class="col-li">
                              {{ subItem.userName }}
                              <span v-if="subItem.teaType == 2">（仲裁）</span>
                            </el-col>
                            <el-col :span="6" class="col-li">
                              <span style="color: #00caaf; font-size: 20px">{{subItem.completeStus}}</span>
                                <span style="font-size: 16px">{{ '/' + subItem.submitcount }}</span>
                            </el-col>
                            <el-col :span="4" class="col-li" :style="{ color: subItem.remnantStu == 0 ? '' : 'red' }"
                              style="font-size: 16px;position: relative;">
                              <div v-if="item.assignType == 5 && subItem.isMerge" class="merge-col"
                                :style="{ height: `${subItem.spanCount}00%` }">{{ subItem.remnantStu }}
                              </div>
                              <span v-if="item.assignType != 5">{{ subItem.remnantStu }}</span>

                            </el-col>

                            <el-col :span="4" class="col-li" style="font-size: 16px">
                              <span :style="{ color: subItem.pendingCompleteCount == 0 ? '' : 'red' }">
                                {{ subItem.pendingCompleteCount }}
                              </span>
                              /{{ subItem.problemPaperCount }}
                            </el-col>
                            <el-col :span="4" class="col-li" style="font-size: 16px">
                              <span :style="{ color: subItem.pendingArbitrationCount == 0 ? '' : 'red' }">
                                {{ subItem.pendingArbitrationCount }} </span>/{{ subItem.arbitrationPaperCount }}
                            </el-col>
                          </el-row>
                        </template>
                      </el-col>
                    </el-row>
                  </template>
                </div>
                <div class="content-height" v-else>
                  <div class="no-data">暂无数据</div>
                </div>
              </div>
            </el-tab-pane>
            <!-- 英语学科展示智批改 -->
            <el-tab-pane label="智批改" name="1" v-if="hasAiCorrect">
              <span style="color:gray">（智批题由AI智能批改，复核老师可以进行复核，未完成复核不影响成绩发布）</span>
              <div class="marking-progress-area-body">
                <el-row class="table-header">
                  <el-col :span="6" class="col-li"> 题目</el-col>
                  <el-col :span="5" class="col-li">
                    AI智批进度
                  </el-col>
                  <el-col :span="5" class="col-li">
                    教师复核进度
                  </el-col>
                  <el-col :span="3" class="col-li"> 复核老师 </el-col>
                  <el-col :span="3" class="col-li"> 已复核/总任务量 </el-col>
                  <el-col :span="2" class="col-li"> 剩余任务量 </el-col>
                </el-row>
                <!--内容-->
                <div class="content-height progress-height" v-if="examMarkDetailsList.length > 0">
                  <template v-for="(item, index) in examMarkDetailsList">
                    <el-row class="table-content" :style="{ height: item.rows.length * 45 + 'px' }">
                      <el-col :span="6" class="col-li summary-col" :title="item.quesName"
                        style="word-break: break-all;">
                        {{
      item.quesName.length > 20 * item.rows.length
        ? item.quesName.substring(0, 20 * item.rows.length) + '...'
        : item.quesName
    }}
                      </el-col>
                      <el-col :span="5" class="col-li summary-col">
                         <el-progress :color="item.aiOver == 1 ? '#67C23A' : '#409EFF'" class="summary-progress"
                          :percentage="item.aiOver*100"></el-progress>
                      </el-col>
                      <el-col :span="5" class="col-li summary-col">
                        <el-progress :color="item.overall == 1 ? '#67C23A' : '#409EFF'" class="summary-progress"
                          :percentage="item.overall"></el-progress>
                      </el-col>
                      <el-col :span="8">
                        <template v-for="(subItem, subIndex) in item.rows">
                          <el-row style="width: 100%; height: 45px; line-height: 45px">
                            <el-col :span="9" class="col-li">
                              {{ subItem.userName }}
                              <span v-if="subItem.teaType == 2">（仲裁）</span>
                            </el-col>
                            <el-col :span="9" class="col-li">
                              <span style="color: #00caaf; font-size: 20px">{{
      subItem.completeStus
    }}</span><span style="font-size: 16px">{{ '/' + subItem.submitcount }}</span>
                            </el-col>
                            <el-col :span="6" class="col-li" :style="{ color: subItem.remnantStu == 0 ? '' : 'red' }"
                              style="font-size: 16px">
                              {{ subItem.remnantStu }}</el-col>
                          </el-row>
                        </template>
                      </el-col>
                    </el-row>
                  </template>
                </div>
                <div class="content-height" v-else>
                  <div class="no-data">暂无数据</div>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>

        </template>
        <div class="marking-progress-area-body">
          <!--阅卷质量-->
          <correct-quality v-if="checkType == 'quality'" :workId="workId"></correct-quality>
          <object-detail v-if="checkType == 'object'" :examId="examId" :workId="workId" :subjectId="subjectId"
            :title="totalDataObj.examTitle + totalDataObj.subjectName"></object-detail>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  canGlanceOver,
  glanceOverMark,
  findExamProgress,
  getSchoolRoleList,
  saveBankJSONAPI,
} from '@/service/api';
import { toDecimal } from '@/utils/number';
import { updateProgress, getExamLeader } from '@/service/pexam';
import { getQueryString } from '@/utils';
import { getBankInfoByPaperNo } from '@/service/testbank';
import correctQuality from './modules/correctQuality.vue';
import objectDetail from './modules/objectDetail.vue';
import UserRole from '@/utils/UserRole';
import { IQUES_SCAN_MODE } from '@/typings/card';

export default {
  name: 'marking-progress',
  data() {
    return {
      totalDataObj: {
        AllsendStudents: 0,
        notAnswerNum: 0,
        AllSubmitStus: 0,
        isOver: 0,
        examTitle: '暂无数据',
        subjectName: this.$route.query.subjectName || '',
      }, //试卷完成数据统计
      examMarkDetailsList: [], //试卷批改进度列表
      tabLoading: false, //表格加载中
      total: 0,
      page: 1,
      limit: 10,
      pageCount: 0, // 页数
      from: this.$route.query.from, //是否来自PC端
      workId: this.$route.query.workId, //试卷id
      //上一个页面的页数
      lastPage: this.$route.query.page,
      //是否可以下载
      isCanDown: false,
      progressTimer: null,
      timerGap: 10000,
      //模式批改 0、主观题阅卷进度（默认） 1、智批改阅卷进度
      smartCorrect: 0,
      examId: getQueryString('examId') || '',
      examName: getQueryString('examName') || '',
      personBookId: getQueryString('personBookId') || '',
      subjectId: getQueryString('subjectId') || '',
      paperNo: getQueryString('paperNo') || '',
      source: getQueryString('source') || '',
      examDateTime: getQueryString('examDateTime') || '',
      year: getQueryString('year') || '',
      baseUrl: process.env.VUE_APP_BASE_API,
      //账号类型
      accountType: '',
      //当前查看类型
      checkType: 'speed',
      isOnlyCorrectTea: false,
      //阅卷质量数据
      qualityData: [],
      qualityTimer: null,
      examCreateId: "",
      examLeaderList: [],
      subjectLeaderList: [],
      hasAiCorrect: getQueryString('aiCorrect') != '0'
    };
  },
  components: {
    correctQuality,
    objectDetail
  },
  computed: {
    //是否为校管、运营、年级组长
    isAdminorOperate() {
      return (
        this.$sessionSave.get('loginInfo') &&
        (this.$sessionSave.get('loginInfo').user_type == 5 ||
          this.$sessionSave.get('loginInfo').admin_type == 2 ||
          this.$sessionSave.get('loginInfo').account_type == 3 ||
          this.$sessionSave.get('loginInfo').account_type == 2)
      );
    },
    // 是否考试管理员
    isExamLeader() {
      let curId = this.$sessionSave.get('loginInfo').id;
      let isOperation = UserRole.isOperation; // 运营
      let isSchoolAdmin = UserRole.isSchoolLeader; // 校领导
      let isExamLeader = this.examLeaderList?.includes(curId); // 考试管理员
      let isSameCreateUser = curId == this.examCreateId; // 创建者
      return isOperation || isSchoolAdmin || isExamLeader || isSameCreateUser;
    },
    isSubjectLeader() {
      return this.subjectLeaderList?.includes(this.$sessionSave.get('loginInfo').id); // 学科管理员
    },
    //是否人机双评
    isAIDoubleEval(){
      return (item)=>{
        return item.scanMode > IQUES_SCAN_MODE.AI_FILL && item.correctMode == 2
      }
    },
  },
  async created() {
    await this.getExamLeader();
  },
  mounted() {
    this.getExamProgress();
    let loginInfo = this.$sessionSave.get('loginInfo');
    this.accountType = loginInfo.account_type;
    this.checkType = this.$sessionSave.get('speedlist_checkType') || 'speed';
  },
  beforeDestroy() {
    this.clearTimer();
  },
  methods: {
    getAssignName(type) {
      let name = '';
      switch (type) {
        case 1:
          name = '平均分配';
          break;
        case 2:
          name = '动态平均';
          break;
        case 3:
          name = '定量分配';
          break;
        case 4:
          name = '按任教班级';
          break;
        case 5:
          name = '效率优先';
          break;
      }
      return name
    },
    // 获取考试管理员
    async getExamLeader() {
      let res = await getExamLeader({
        examId: this.examId,
      });
      this.examLeaderList = res.data.examLeaderList.map(item => item.id) || [];
      this.subjectLeaderList = res.data[this.subjectId].leaderList.map(item => item.id) || [];
      this.examCreateId = res.data.examInfo.createUserId;
    },
    /***
     * @name:返回上一页
     */
    backReport() {
      this.$sessionSave.remove('speedlist_checkType')
      this.clearTimer();
      this.$router.back();
    },
    handleClick() {
      this.clearTimer();
      this.getExamProgress();
    },
    changeCheckType() {
      this.$sessionSave.set('speedlist_checkType', this.checkType);
    },
    /**
     * 是否结束阅卷
     */
    endExamMarkold(row) {
      let _this = this;
      let params = {
        userId: this.$sessionSave.get('loginInfo').id,
        schoolId: this.$sessionSave.get('schoolInfo').id,
        workId: _this.workId,
      };
      canGlanceOver(params)
        .then(
          function (result) {
            if (result.code == -1) {
              _this.$Message.error(result.msg);
            } else {
              _this.$Modal.confirm({
                title: '提示',
                content:
                  "<p style='font-size:16px;margin-top: -6px;'>结束阅卷，将无法继续阅卷或修改成绩，确定结束吗？</p>",
                onOk: () => {
                  glanceOverMark(params)
                    .then(
                      function (result) {
                        if (result && parseInt(result.code) === 1) {
                          _this.$Message.success('阅卷已结束');
                          if (_this.from == 'pc') {
                            _this.getExamProgress();
                          } else {
                            //返回上一页
                            _this.$router.back(-1);
                          }
                        } else {
                          _this.$Message.error(result.msg);
                        }
                      },
                      function (err) {
                        _this.$Message.error(err.toString());
                      }
                    )
                    .catch(function (error) {
                      console.log(error);
                    });
                },
                onCancel: () => { },
              });
            }
          },
          function (err) {
            _this.$Message.error(err.toString());
          }
        )
        .catch(function (error) {
          console.log(error);
        });
    },
    endExamMark(row) {
      let _this = this;
      let params = {
        examId: _this.examId,
        progress: 5,
        personalBookId: this.personBookId,
        progressState: 1,
        schoolId: _this.$sessionSave.get('schoolInfo').id,
      };
      _this.$Modal.confirm({
        title: '提示',
        content:
          "<p style='font-size:16px;margin-top: -6px;'>结束阅卷，将无法继续阅卷或修改成绩，确定结束吗？</p>",
        onOk: () => {
          _this.stopCorrect();
        },
        onCancel: () => { },
      });
    },
    /**
     * @name:结束阅卷
     */
    async stopCorrect() {
      let _this = this;

      const params = {
        schoolId: this.$sessionSave.get('schoolInfo').id,
        phase: '',
        year: this.year,
        systemCode: '',
        subjectId: this.subjectId,
        roleType: 2,
      };
      //获取备课组长userid
      const result = await getSchoolRoleList(params);
      if (result.code == 1) {
        if (result.data.length > 0) {
          let userId = result.data[0].userId;
          //结束阅卷创建作业
          await updateProgress({
            examId: _this.examId,
            progress: 5,
            personalBookId: _this.personBookId,
            progressState: 1,
            schoolId: _this.$sessionSave.get('schoolInfo').id,
          })
            .then(
              function (result) {
                if (result && parseInt(result.code) === 1) {
                  // _this.saveWorkJSON(userId); 25/02/20 周龙要求注释此行代码
                  _this.$Message.success('阅卷已结束');
                  if (_this.from == 'pc') {
                    _this.getExamProgress();
                  } else {
                    //返回上一页
                    _this.$router.back(-1);
                  }
                } else {
                  _this.$Message.error(result.msg);
                }
              },
              function (err) {
                _this.$Message.error(err.toString());
              }
            )
            .catch(function (error) {
              console.log(error);
            });
        } else {
          _this.$message({
            message: '请先设置备课组长！',
            type: 'error',
            duration: 1500,
          });
        }
      } else {
        _this.$message({
          message: '请求失败！',
          type: 'error',
          duration: 1000,
        });
      }
    },
    /**
     * @name: 保存作业
     * */
    async saveWorkJSON(userId) {
      let paperNum = 0;
      let res = await getBankInfoByPaperNo({ paperNo: this.paperNo });
      if (res && res.code == 1) {
        paperNum = res.data.paperNum;
      }
      let workJson = this.buildSendOptoions();
      await saveBankJSONAPI({
        darftId: '',
        state: 0, //0 定时 1:已发送 2：草稿箱[默认] 来源
        userId: userId, //备课组长userId
        creatType: 2, // creat_type表示的是创建什么样的作业,是普通作业还是备课组长作业,云考试全为1
        sendSource: 2, // PC
        correctType: 0, //批改类型  0:老师自批改，1：全班互批，2：学生自批
        permissionType: 0, //学生作业互看状态（0:允许互看优秀作答，1:允许互看 2:不允许互看）
        autoSubmit: 0,
        firstType: 3,
        subjectId: this.subjectId,
        workJson: workJson,
        sourceId: this.personBookId,
        paperNo: this.paperNo,
        paperNum: paperNum,
        relationId: this.examId,
      })
        .then(res => { })
        .catch(err => {
        });
    },
    /**
     * @name: 构建workJson数据
     * @return 发送数据
     */
    buildSendOptoions() {
      let examTime = this.examDateTime;
      let date = new Date(examTime);
      date.setDate(date.getDate() + 1);
      let dateStr = this.$formatDate(date);
      let quesData = {
        darftid: '',
        cardid: '',
        title: this.examName,
        classes: [],
        objectList: [],
        classnames: [],
        groups: [],
        stuList: [],
        uid: this.$sessionSave.get('loginInfo').id,
        queSource: 0,
        sendType: 1,
        answerType: 0,
        startTime: `${examTime} 08:00`,
        endtime: `${dateStr} 08:00`,
        answerTime: '',
        hwTypeCode: '104',
        xkwPaperId: '',
        openId: '',
        remarks: '',
        docids: '',
        anwserdocids: '',
        year: '',
        subjectId: this.subjectId,
        markingType: this.source == '3' ? 1 : 2,
        quesdatas: [],
      };
      return JSON.stringify(quesData);
    },
    /**
     *获取阅卷进度列表
     */
    async getExamProgress() {
      let _this = this;
      let params = {
        schoolId: _this.$sessionSave.get('schoolInfo').id,
        workId: _this.workId,
        smartCorrect: _this.smartCorrect,
        markingType: 2, //阅卷方式 1：手阅  2：网阅  3：在线作业
        isHide: this.isOnlyCorrectTea ? 1 : 0,//0:不过滤 1：过滤未开始阅卷
      };
      _this.tabLoading = true;
      let result = await findExamProgress(params);
      if (result.code == 1) {
        let data = result.data;
        if (Object.keys(data.headList).length != 0) {
          _this.totalDataObj.AllsendStudents = data.headList.allsendStudents;
          _this.totalDataObj.AllSubmitStus = data.headList.allSubmitStus;
          _this.totalDataObj.notAnswerNum =
            data.headList.allsendStudents - data.headList.allSubmitStus;
          _this.totalDataObj.isOver = data.headList.isOver;
          _this.totalDataObj.examTitle = data.headList.examTitle;
        }
        data.rowsList.forEach(item => {
          item.overall = toDecimal(item.overall * 100);
          item.rows.forEach(subItem => {
            if (subItem.problemPaperCount > 0) {
              _this.isCanDown = true;
            }
          });
        });
        _this.examMarkDetailsList = data.rowsList;
        _this.getProgressState(params);
      } else {
        _this.tabLoading = false;
        console.log(error);
        _this.$Message.error('请求出现异常');
      }
    },
    async getProgressState(params) {
      const res = await findExamProgress(params);
      if (res.code == 1 && res.data.rowsList.length) {
        res.data.rowsList.forEach((item, index) => {
          this.examMarkDetailsList.forEach((ite, inde) => {
            if (index == inde) {
              this.$set(ite, 'overall', toDecimal(item.overall * 100));
              this.$set(ite, 'rows', item.rows.length ? item.rows : ite.rows);
            }
          });
          let isMergeCount = false;
          item.rows.forEach(subItem => {
            if (subItem.problemPaperCount > 0) {
              this.isCanDown = true;
            }
            if (item.assignType == 5) {
              //效率优先获取共同任务列数
              if (!isMergeCount) {
                isMergeCount = true;
                this.$set(subItem, 'isMerge', true)
                this.$set(subItem, 'spanCount', item.rows.length)
              }
            }
          });
        });
        if (!this.checkProgressState(this.examMarkDetailsList)) {
          this.progressTimer = setTimeout(() => {
            this.getProgressState(params);
          }, this.timerGap);
        } else {
          this.clearTimer();
        }
      }
    },
    /**
     * @name:获取批阅进度
     */
    checkProgressState(data) {
      let dataLength = data.length;
      for (let i = 0; i < dataLength; i++) {
        let item = data[i];
        if (item.overall != 100) {
          return false;
        }
      }
      return true;
    },
    /**
     * @name：清除定时器
     */
    clearTimer() {
      if (this.progressTimer) {
        clearTimeout(this.progressTimer);
        this.progressTimer = null;
      }
    },
    /**
     * 下载问题卷学生名单
     */
    downLoadExcel: function () {
      if (this.isCanDown) {
        let url =
          this.baseUrl +
          '/homework/cloudexam/downExamStuList?schoolId=' +
          this.$sessionSave.get('schoolInfo').id +
          '&workId=' +
          this.workId +
          '&type=1';
        let iframe = document.createElement('iframe');
        iframe.style.display = 'none';
        iframe.src = url;
        console.log(url);
        iframe.onload = function () {
          document.body.removeChild(iframe);
        };
        document.body.appendChild(iframe);
      } else {
        this.$Message.error('暂无问题卷名单');
        return;
      }
    }
  },
};
</script>

<style lang="scss" scoped>
.allocate-container {
  font-size: 14px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  // background: #f0eff2;

  .allocate-content {
    display: flex;
    margin-top: 10px;
  }

  .marking-progress-area {
    width: 100%;
    background: #fff;
    position: relative;

    .marking-progress-area-header {
      display: flex;
      width: 100%;
      height: 50px;
      padding-left: 25px;
      line-height: 50px;
      border-bottom: 1px solid #f6f6f6;

      .paper-title {
        font-size: 14px;
        font-weight: bold;
        color: #333;
        max-width: 360px;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
      }

      .data-area {
        display: flex;
        align-items: center;
        margin-left: 45px;

        .data-item {
          padding-right: 30px;
          // padding: 0 38px;
          // margin-right: 13px;
          height: 34px;
          line-height: 34px;
          border-radius: 5px;
        }

        .sub-num {
          background: #eefffd;
        }

        .send-num {
          background: #ffefef;
        }

        .total-num {
          background: #f8f8f9;
        }

        .total-number {
          font-size: 18px;
        }

        .sub-number {
          font-size: 18px;
          color: #00caaf;
        }

        .send-number {
          font-size: 18px;
          color: #ff4e4e;
        }

        .data-progress {
          width: 200px;
          margin-left: 20px;
        }
      }
    }

    .marking-progress-area-filter {
      text-align: right;
      margin-right: 30px;
    }

    .marking-progress-area-body {
      margin: 20px 25px;

      .col-li {
        text-align: center;
        border-right: 1px solid #f3f3f3;
        border-bottom: 1px solid #f3f3f3;
        height: 100%;
        vertical-align: middle;
        display: flex;
        align-items: center;
        justify-content: center;
        align-content: center;

        .merge-col {
          top: 0;
          position: absolute;
          display: flex;
          align-items: center;
          background: #fff;
          width: 100%;
          justify-content: center;
          z-index: 1;
        }
      }

      .ques-col {
        flex-direction: column;
        justify-content: unset;

        >span {
          line-height: 25px;
        }
      }

      .summary-col:first-child {
        border-left: 1px solid #f3f3f3;
      }

      .table-header {
        height: 50px;
        line-height: 50px;
        background: #f8f8f9;
        border-radius: 5px 5px 0 0;
      }

      .content-height {
        // height: 612px;
        margin-top: 1px;
        overflow-y: auto;
      }
    }
  }
}

.no-data {
  height: 80px;
  line-height: 80px;
  text-align: center;
  border: 1px solid #f3f3f3;
  border-top: 0;
}

.end-marking-button {
  position: absolute;
  right: 50px;
  top: 50px;
  padding: 6px 10px !important;
}

.check-type-radio {
  margin: 10px 10px 10px 25px;
}

.correct-tab {
  padding: 0px 25px;
}
</style>
<style lang="scss">
.table-content {
  .ivu-progress-bg {
    background-color: #00caaf !important;
  }

  .ivu-icon {
    width: 25px !important;
  }

  .ivu-progress-text {
    width: 25px !important;
  }
}

.summary-progress {
  width: 100%;

  .el-progress-bar {
    padding-right: 66px !important;
  }

  .el-progress__text {
    margin-left: unset !important;
    width: 45px;
  }
}
</style>