<!--
 * @Description: 
 * @Author: 小圆
 * @Date: 2025-03-17 09:50:35
 * @LastEditors: 小圆
-->
<template>
  <div class="composition-eva" v-loading="isLoading" @keyup.enter="submit" tabindex="0">
    <template v-if="curStu">
      <div class="stu-main">
        <div class="stu-score">
          <span v-if="curStu.isProblemPaper">问题卷</span>
          <span v-else>{{ curStu.stuScores }}分</span>
        </div>

        <el-image
          v-if="curStu.evaluateState === EVA_STATE.WELL"
          class="evaluate-icon"
          :src="require('@/assets/correct/well_max_icon.png')"
        ></el-image>
        <el-image
          v-if="curStu.evaluateState === EVA_STATE.WRONG"
          class="evaluate-icon"
          :src="require('@/assets/correct/wrong_max_icon.png')"
        ></el-image>

        <el-image
          v-for="item in curStu.stuNewResList"
          class="correct-answer-img"
          :key="item.source"
          :src="replaceALiUrl(item.source)"
          @click="previewImg(item.source, 1, 1)"
        ></el-image>
      </div>
      <div class="stu-aside">
        <div class="stu-page">
          <span v-if="smartType == REVIEW_STATE.WAIT_REVIEW">
            <span v-if="!scoreOption || scoreOption.score === ''">
              {{ `${(curQues.submitStudents - curQues.errorNotReviewed || 0) + 1}/${curQues.submitStudents || 0}份` }}
            </span>
            <span v-else>
              {{ `${stuIndex + 1}/${pagination.total_rows}份` }}
            </span>
          </span>
          <span v-else>
            {{ `${stuIndex + 1}/${pagination.total_rows}份` }}
          </span>
        </div>

        <div v-if="curStu.evaluation" class="evaluation-section">
          <p class="evaluation-title">判分依据：</p>
          <div class="evaluation-content" :class="{ 'is-collapsed': isEvaluationCollapsed && shouldShowExpandBtn }">
            <p class="evaluation-text" ref="evaluationText">
              {{ curStu.evaluation }}
            </p>
          </div>
          <div v-if="shouldShowExpandBtn" class="evaluation-toggle" @click="toggleEvaluation">
            {{ isEvaluationCollapsed ? '查看更多' : '收起' }}
            <i :class="isEvaluationCollapsed ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
          </div>
        </div>

        <div class="stu-score-info" v-if="isComposition">
          <template v-if="subjectCenterCode == 'ENGLISH'">
            <p class="stu-score-item">内容分：{{ curStu.smartScore.cscore + curStu.smartScore.wscore }}</p>
            <p class="stu-score-item">语言分：{{ curStu.smartScore.lscore }}</p>
            <p class="stu-score-item">结构分：{{ curStu.smartScore.oscore }}</p>
          </template>
          <template v-else>
            <p class="stu-score-item" v-for="item in curStu.ans" :key="item.angle">
              {{ item.angle }}：{{ item.score }}分
            </p>
          </template>
        </div>
        <div class="stu-score-detail" v-if="isComposition" @click="showDetail">查看详细点评 ></div>
        <div class="stu-score-modify">
          <div class="stu-score-input-row">
            <el-input-number
              class="stu-score-input"
              v-model="curStu.stuScores"
              size="small"
              :disabled="curStu.isHandleComplete"
              :placeholder="`满分${curQues.quesScore}分`"
              :min="0"
              :max="curQues.quesScore"
              :controls="false"
              @change="checkScore"
              @keyup.enter.native="submit"
            ></el-input-number>
            <div
              class="score-block score-block-right"
              :class="{ active: curStu.stuScores === curQues.quesScore }"
              @click="curStu.stuScores = curQues.quesScore"
            >
              <i class="el-icon-check"></i>
            </div>
            <div
              class="score-block score-block-error"
              :class="{ active: curStu.stuScores === 0 }"
              @click="curStu.stuScores = 0"
            >
              <i class="el-icon-close"></i>
            </div>
          </div>

          <div class="score-blocks">
            <div
              v-for="(item, index) in quesScoreArr"
              :key="index"
              class="score-block"
              :class="{ active: curStu.stuScores === item }"
              @click="curStu.stuScores = item"
            >
              {{ item }}
            </div>
          </div>
        </div>
        <div class="stu-paper-types">
          <el-button
            size="small"
            :type="curStu.evaluateState === EVA_STATE.WELL ? 'success' : 'default'"
            @click="handleEva(EVA_STATE.WELL)"
            >优秀</el-button
          >
          <el-button
            size="small"
            :type="curStu.evaluateState === EVA_STATE.WRONG ? 'danger' : 'default'"
            @click="handleEva(EVA_STATE.WRONG)"
            >典型</el-button
          >
          <el-button size="small" :type="curStu.isProblemPaper ? 'primary' : 'default'" @click="handleProblemPaper">
            问题卷
          </el-button>
        </div>
        <div class="next-btn-container">
          <el-button type="primary" round @click="switchPrevStu" :disabled="stuIndex === 0">上一份</el-button>
          <el-button type="primary" round @click="submit" :loading="submitLoading">下一份</el-button>
        </div>
      </div>
    </template>

    <template v-else>
      <no-data type="empty" text="该题已阅完，您辛苦了~" style="background-color: #fff"></no-data>
    </template>

    <el-dialog :visible.sync="isShowDetail" title="详细点评" width="900px" class="composition-eva-dialog">
      <CompositionAnalyseDetail
        :workId="$route.query.workId"
        :stuId="curStu.stuId"
        :stuNo="curStu.stuNo"
        :quesNo="curQues.quesNo"
        :tQuesNo="curQues.tQuesNo"
        v-if="isShowDetail"
      ></CompositionAnalyseDetail>
    </el-dialog>

    <ImgPreview v-if="isShowPreviewImg" :imgList="previewImgList" @close="isShowPreviewImg = false"> </ImgPreview>
  </div>
</template>

<script lang="ts">
import { Component, Mixins, Prop, Ref, Vue } from 'vue-property-decorator';
import CompositionAnalyseDetail from '../lookReport/compositionAnalyse/compositionAnalyseDetail.vue';
import { getSmartCorrectStuList, saveExamCorrectDataAPI } from '@/service/api';
import { EVA_STATE, SMART_TYPE, SOURCE_TYPE, ScoreOption, SmartCorrectStu, TeaCorrectQues } from './types';
import { replaceALiUrl } from '@/utils/common';
import NoData from '@/components/noData.vue';
import CorrectMixin from './correct.mixin.vue';
import { supportsAvif } from '@/utils/index';
import { IQUES_SCAN_MODE } from '@/typings/card';

function limitDecimalPlaces(num, decimalPlaces) {
  const factor = Math.pow(10, decimalPlaces);
  return Math.round(num * factor) / factor;
}

// 预加载
function preloadImg(src) {
  let image = new Image();
  image.crossOrigin = 'anonymous';
  image.src = replaceALiUrl(src);
  image.onload = () => {
    image = null;
  };
  image.onerror = () => {
    image = null;
  };
}

// 根据步长生成到数值数组
const generateArrayWithStep = (number: number, step: number = 1) => {
  let array = [];
  for (let i = 0; i < number; i += step) {
    array.push(i);
  }
  array.push(number);
  return array;
};

@Component({
  components: {
    CompositionAnalyseDetail,
    NoData,
  },
})
export default class CompositionEva extends Mixins(CorrectMixin) {
  /** 当前题目 */
  @Prop({ default: () => null }) curQues: TeaCorrectQues | null;
  /** 批改状态 0:待复核 1:已复核 */
  @Prop({ default: SMART_TYPE.WAIT_REVIEW }) smartType: SMART_TYPE;
  /** 筛选的学生得分 */
  @Prop() scoreOption: ScoreOption | null;
  /** 学科中心编码 */
  @Prop({ default: 'ENGLISH' }) subjectCenterCode: string;
  /** 来源 */
  @Prop({ default: SOURCE_TYPE.TEACHER }) source: SOURCE_TYPE;
  /** 班级ID */
  @Prop({ default: '' }) classId: string;
  // 学生列表
  stuList: SmartCorrectStu[] = [];
  // 当前学生
  curStu: SmartCorrectStu | null = null;
  // 当前学生索引
  stuIndex = 0;

  // 分页器
  pagination = {
    page: 1, // 页码
    limit: 50, // 每页数据大小
    total_rows: 0, // 数据总数
  };

  // 提交按钮loading
  submitLoading = false;
  // 是否显示详细点评
  isShowDetail = false;
  // 是否正在加载
  isLoading = false;
  // 替换阿里云图片
  replaceALiUrl = replaceALiUrl;
  // 教师评价状态
  EVA_STATE = EVA_STATE;
  REVIEW_STATE = SMART_TYPE;
  // 判分依据展开/收起状态
  isEvaluationCollapsed = true;
  // 最大行数（超过此行数时显示展开按钮）
  maxEvaluationLines = 5;
  // 实际行数
  actualEvaluationLines = 0;

  // 分数数组
  get quesScoreArr() {
    let quesScore = this.curQues.quesScore;
    let step = this.curQues.step ? this.curQues.step : 1;
    let arr = generateArrayWithStep(quesScore, step);
    return arr;
  }

  // 是否作文题
  get isComposition() {
    return this.curQues.isAiCorrect == 3 || this.curQues.scanMode == IQUES_SCAN_MODE.AI_ESSAY;
  }

  // 是否应该显示展开按钮
  get shouldShowExpandBtn() {
    return this.curStu?.evaluation && this.actualEvaluationLines > this.maxEvaluationLines;
  }

  // 初始化批改
  async initCorrect() {
    this.stuList = [];
    this.stuIndex = 0;
    this.pagination.page = 1;
    this.pagination.total_rows = 0;

    const stuList = await this.getStuList({ page: 1 });
    this.stuList = this.stuList.concat(stuList);

    if (
      this.stuList.length &&
      (!this.scoreOption || this.scoreOption.score === '') &&
      this.smartType == SMART_TYPE.WAIT_REVIEW
    ) {
      const reStuList = await this.getStuList({ page: 1, smartType: SMART_TYPE.DONE_REVIEW });
      this.stuList = reStuList.reverse().concat(this.stuList);
      this.stuIndex = reStuList.length;
    }
    this.switchStu(this.stuList[this.stuIndex]);
  }

  // 切换下一位学生
  async switchNextStu() {
    // 如果没有下一位学生
    if (!this.stuList[this.stuIndex + 1]) {
      // 如果是待复核，则重置页码，否则是已复核，则页码+1
      if (this.smartType == SMART_TYPE.WAIT_REVIEW) {
        this.pagination.page = 1;
      } else {
        this.pagination.page++;
      }
      // 获取下一批学生数据
      const stuList = await this.getStuList({ page: this.pagination.page });
      this.stuList = this.stuList.concat(stuList);
    }

    this.stuIndex++;
    this.switchStu(this.stuList[this.stuIndex]);

    // 如果是待复核，则错误未复核数-1
    if (this.smartType == SMART_TYPE.WAIT_REVIEW) {
      this.curQues.errorNotReviewed--;
      if (this.scoreOption && this.scoreOption.count) this.scoreOption.count--;

      if (this.curQues.errorNotReviewed <= 0) {
        this.$emit('submit-next');
      }
    }
  }

  // 切换上一人
  switchPrevStu() {
    if (this.stuIndex > 0) {
      this.stuIndex--;
      this.switchStu(this.stuList[this.stuIndex]);
      this.curQues.errorNotReviewed++;
      if (this.scoreOption) this.scoreOption.count++;
    }
  }

  // 切换学生
  switchStu(stu) {
    this.curStu = stu;
    // 重置判分依据展开状态
    this.isEvaluationCollapsed = true;
    // 重置行数
    this.actualEvaluationLines = 0;

    // 在下一个tick计算行数
    this.$nextTick(() => {
      this.calculateEvaluationLines();
    });

    const nextStu = this.stuList[this.stuIndex + 1];
    if (nextStu) {
      nextStu?.stuNewResList.forEach(item => {
        preloadImg(item.source);
      });
    }
  }

  // 切换判分依据展开/收起状态
  toggleEvaluation() {
    this.isEvaluationCollapsed = !this.isEvaluationCollapsed;
  }

  // 计算评价文本的行数
  calculateEvaluationLines() {
    if (!this.curStu?.evaluation || !this.$refs.evaluationText) {
      this.actualEvaluationLines = 0;
      return;
    }

    const element = this.$refs.evaluationText as HTMLElement;
    if (!element) {
      this.actualEvaluationLines = 0;
      return;
    }

    // 获取元素的计算样式
    const computedStyle = window.getComputedStyle(element);
    const lineHeight = parseFloat(computedStyle.lineHeight);
    const elementHeight = element.scrollHeight;

    // 计算行数
    this.actualEvaluationLines = Math.ceil(elementHeight / lineHeight);
  }

  // 获取学生列表
  async getStuList(option?: { page?: number; limit?: number; smartType?: number }) {
    if (!option) option = {};
    option.page = option.page ?? this.pagination.page;
    option.limit = option.limit ?? this.pagination.limit;
    option.smartType = option.smartType ?? this.smartType;

    let isSupportsAvif = await supportsAvif('');
    this.isLoading = true;
    let params: any = {
      type: 1,
      workId: this.$route.query.workId,
      userId: this.$sessionSave.get('loginInfo').id,
      quesId: this.curQues.quesId,
      quesLevel: this.curQues.quesLevel,
      schoolId: this.$sessionSave.get('schoolInfo').id,
      page: option.page,
      sort: 0,
      limit: option.limit,
      smartType: option.smartType,
      stuScore: this.scoreOption?.score,
      order: option.smartType == SMART_TYPE.WAIT_REVIEW ? 0 : 1, // 升序(默认)  1 降序
      correctVersion: isSupportsAvif ? 4 : 5, // 打开AVIF格式图片 4打开 5关闭
    };
    if (this.source == SOURCE_TYPE.CLASS) {
      params.classId = this.classId;
      params.source = this.source;
    }
    let stuList = [];
    try {
      const res = await getSmartCorrectStuList(params);
      if (this.checkPause(res.code, res.msg)) return;
      stuList = res.data.rows;
      this.pagination.total_rows = res.data.total_rows;
    } catch (e) {
      stuList = [];
    } finally {
      this.isLoading = false;
    }
    return stuList;
  }

  // 查看详细点评
  showDetail() {
    this.isShowDetail = true;
  }

  // 输入得分
  async checkScore() {
    let stuScore = this.curStu.stuScores.toString();
    let decimal = 1;
    if (stuScore.split('.')[1] && stuScore.split('.')[1].length > decimal) {
      let score = limitDecimalPlaces(this.curStu.stuScores, decimal);
      await this.$nextTick();
      this.curStu.stuScores = score;
    }
  }

  // 教师评价
  handleEva(state: number) {
    let curState: number = this.curStu.evaluateState;
    if (state === EVA_STATE.WELL) {
      // 优秀作答
      curState = curState !== EVA_STATE.WELL ? EVA_STATE.WELL : EVA_STATE.NOT;
    } else {
      // 典型错误
      curState = curState !== EVA_STATE.WRONG ? EVA_STATE.WRONG : EVA_STATE.NOT;
    }
    this.curStu.evaluateState = curState;
  }

  // 问题卷
  handleProblemPaper() {
    if (this.curStu.isHandleComplete) {
      return this.$message.warning('该问题卷已经被处理完成');
    }

    if (!this.curStu.isProblemPaper) {
      this.curStu.isProblemPaper = 1;
      this.curStu.stuScores = 0;
      this.curStu.proCode = '作文智批异常';
      this.curStu.proRemark = '';
    } else {
      this.curStu.isProblemPaper = 0;
      this.curStu.proCode = '';
      this.curStu.proRemark = '';
    }
  }

  // 提交
  async submit() {
    if (this.submitLoading || !this.curStu || this.isLoading) return false;
    await this.checkScore();
    this.submitLoading = true;
    let isMarkProblem = this.curStu.isProblemPaper;
    let problemType = this.curStu.isProblemPaper;
    let proCode = this.curStu.proCode;
    let proRemark = this.curStu.proRemark;
    let sources: Array<any> = [];
    this.curStu.stuNewResList.forEach((item: any) => {
      sources.push({
        resId: item.resId,
        resType: item.resType,
        source: item.source,
      });
    });

    let json = {
      quesLevel: this.curQues.quesLevel,
      quesId: this.curQues.quesId,
      score: this.curStu.stuScores,
      isright: this.curStu.stuScores == this.curQues.quesScore ? 1 : 0,
      evaluateState: this.curStu.evaluateState,
      comment: this.curStu.comment,
      sources: sources,
      problemType: problemType,
      isMarkProblem: isMarkProblem,
      proCode: proCode,
      proRemark: proRemark,
      correctType: 0,
    };

    let params: any = {
      schoolId: this.$sessionSave.get('schoolInfo').id,
      workId: this.$route.query.workId,
      shwId: this.curStu.shwId,
      correctJson: JSON.stringify([json]),
      userId: this.$sessionSave.get('loginInfo').id,
      realname: this.$sessionSave.get('loginInfo').realname,
      correctVersion: 4,
    };

    if (this.source == SOURCE_TYPE.CLASS) {
      params.source = this.source;
    }

    const res = await saveExamCorrectDataAPI(params);
    if (this.checkPause(res.code, res.msg)) return;
    this.submitLoading = false;
    this.switchNextStu();
  }
}
</script>

<style scoped lang="scss">
.stu-btn-group {
  position: absolute;
  top: 0;
  right: 0;
}

.composition-eva {
  flex: 1;
  display: flex;
  background-color: #f5f7fa;
  overflow: auto;

  &:focus {
    outline: none;
  }
}

.stu-main {
  position: relative;
  width: 0;
  height: calc(100% - 32px);
  flex: 1;
  padding: 20px;
  background-color: #fff;
  margin: 16px;
  border-radius: 8px;
  overflow: auto;
}

.stu-aside {
  width: 263px;
  height: calc(100% - 32px);
  padding: 20px;
  background-color: #fff;
  margin: 16px 16px 16px 0;
  border-radius: 8px;
  overflow: auto;
}

.stu-score {
  position: absolute;
  top: 20px;
  right: 20px;
  font-size: 30px;
  font-weight: bold;
  color: red;
  z-index: 1;
}

.evaluate-icon {
  position: absolute;
  top: 20px;
  right: 130px;
  z-index: 1;
}

.stu-page {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 20px;
}

.evaluation-section {
  margin-bottom: 20px;
  padding: 16px;
  background: #f8fafe;
  border: 1px solid #e8f2ff;
  border-radius: 8px;

  .evaluation-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    margin: 0 0 12px 0;
    display: flex;
    align-items: center;

    &::before {
      content: '';
      width: 4px;
      height: 16px;
      background: #409eff;
      border-radius: 2px;
      margin-right: 8px;
    }
  }

  .evaluation-content {
    position: relative;
    transition: all 0.3s ease;

    &.is-collapsed {
      max-height: 89.6px; // 4行文字的高度 (14px * 1.6 * 4 = 89.6px)
      overflow: hidden;

      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 24px;
        background: linear-gradient(transparent, #f8fafe);
        pointer-events: none;
      }
    }

    .evaluation-text {
      font-size: 14px;
      line-height: 1.6;
      color: #606266;
      margin: 0;
      word-break: break-word;
      white-space: pre-wrap;
    }
  }

  .evaluation-toggle {
    margin-top: 8px;
    color: #409eff;
    font-size: 13px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 4px 0;
    transition: all 0.2s ease;

    &:hover {
      color: #66b1ff;
      background: rgba(64, 158, 255, 0.1);
      border-radius: 4px;
    }

    i {
      margin-left: 4px;
      font-size: 12px;
      transition: transform 0.2s ease;
    }
  }
}

.stu-score-info {
  margin-bottom: 20px;

  .stu-score-item {
    font-size: 15px;
    color: #606266;
    margin-bottom: 10px;
  }
}

.stu-score-detail {
  color: #409eff;
  cursor: pointer;
  margin-bottom: 20px;
  font-size: 14px;

  &:hover {
    opacity: 0.8;
  }
}

.stu-score-modify {
  display: flex;
  flex-direction: column;
  margin-bottom: 20px;
  font-size: 14px;
  color: #606266;
  padding: 10px;
  background: #f8f8f8;

  :deep(.el-input) {
    width: 120px;
    height: 40px;

    .el-input__inner {
      height: 40px;
      line-height: 40px;
    }
  }
}

.stu-score-input-row {
  display: flex;
  align-items: center;
  gap: 4px;
}

.score-blocks {
  display: flex;
  flex-wrap: wrap;
  margin-top: 10px;
  max-height: 240px;
  overflow-y: auto;
}

.score-block {
  width: 40px;
  height: 40px;
  border: 1px solid #dcdfe6;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s;
  background-color: #fff;

  &:hover {
    border-color: #409eff;
    color: #409eff;
  }

  &.active {
    background-color: #409eff;
    color: white;
    border-color: #409eff;
  }

  &.score-block-right.active {
    background-color: #67c23a;
    color: white;
    border-color: #67c23a;
  }

  &.score-block-error.active {
    background-color: #f56c6c;
    color: white;
    border-color: #f56c6c;
  }
}

.stu-paper-types {
  display: flex;
  margin-bottom: 30px;
}

.next-btn-container {
  text-align: center;
  display: flex;
  justify-content: space-evenly;
  gap: 10px;

  :deep(.el-button) {
    margin-left: 0;
  }
}

.correct-answer-img {
  cursor: pointer;
  user-select: none;
  -webkit-user-drag: none;
  -khtml-user-drag: none;
  -moz-user-drag: none;
  -o-user-drag: none;
}

.composition-eva-dialog {
  :deep(.el-dialog__body) {
    padding: 0;
  }
}
</style>
